import { z } from "zod";

/* ------------------ Enums ------------------ */
export const verificationTypeSchema = z.enum([
  "EMAIL_VERIFICATION",
  "PASSWORD_RESET",
]);
export const verificationMethodSchema = z.enum(["EMAIL", "SMS", "WHATSAPP"]);
export const verificationStatusSchema = z.enum([
  "PENDING",
  "VERIFIED",
  "EXPIRED",
]);
export const userRoleSchema = z.enum(["ADMIN", "USER", "OWNER"]);

/* ------------------ <PERSON><PERSON><PERSON> (Helpers) ------------------ */
const passwordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters")
  .regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
    "Password must contain a lowercase, uppercase, and a number"
  );

const phoneSchema = z
  .string()
  .regex(/^(\+62|62|0)[0-9]{8,13}$/, "Invalid Indonesian phone number format");

const deviceInfoSchema = z
  .object({
    os: z.string().optional(),
    browser: z.string().optional(),
    device: z.string().optional(),
  })
  .strict();

/* ------------------ Skema Entitas Utama ------------------ */
// Skema ini mewakili data user di database (termasuk password)
const userDbSchema = z
  .object({
    id: z.cuid2(),
    email: z.string().email(),
    password: z.string(),
    firstName: z.string().max(50).nullish(),
    lastName: z.string().max(50).nullish(),
    role: userRoleSchema.default("USER"),
    phone: phoneSchema.nullish(),
    isActive: z.boolean().default(true),
    isEmailVerified: z.boolean().default(false),
    createdAt: z.coerce.date(),
    updatedAt: z.coerce.date(),
  })
  .strict();

// PENTING: Skema ini untuk data user yang aman dikirim ke client (TANPA password)
export const publicUserSchema = userDbSchema.omit({ password: true });

export const userSessionSchema = z
  .object({
    id: z.cuid2(),
    userId: z.cuid2(),
    sessionToken: z.string(),
    deviceInfo: deviceInfoSchema.optional(),
    deviceFingerprint: z.string().optional(),
    ipAddress: z.string(),
    location: z.record(z.string(), z.any()).optional(),
    userAgent: z.string().optional(),
    isActive: z.boolean(),
    lastActivity: z.coerce.date(),
    expiresAt: z.coerce.date(),
    loginMethod: z.string().optional(),
    isTrusted: z.boolean().default(false),
    createdAt: z.coerce.date(),
    updatedAt: z.coerce.date(),
  })
  .strict();

export const userSecuritySchema = z
  .object({
    id: z.cuid2(),
    userId: z.cuid2(),
    failedLoginAttempts: z.number().int().min(0).default(0),
    lastFailedLoginAt: z.coerce.date().optional(),
    accountLockedUntil: z.coerce.date().optional(),
    lastLoginAt: z.coerce.date().optional(),
    lastLoginIP: z.ipv4().optional(),
    lastLoginLocation: z.record(z.string(), z.any()).optional(),
    lastLoginDevice: z.record(z.string(), z.any()).optional(),
    twoFactorEnabled: z.boolean().default(false),
    twoFactorMethod: verificationMethodSchema.optional(),
    twoFactorSecret: z.string().optional(),
    twoFactorBackupCodes: z.array(z.string()).default([]),
    tempSetupToken: z.string().optional(),
    tempSetupSecret: z.string().optional(),
    tempSetupExpiresAt: z.coerce.date().optional(),
    trustedDevices: z.array(z.string()).default([]),
    createdAt: z.coerce.date(),
    updatedAt: z.coerce.date(),
  })
  .strict();

export const userVerificationSchema = z
  .object({
    id: z.cuid2(),
    userId: z.cuid2(),
    type: verificationTypeSchema,
    method: verificationMethodSchema.default("EMAIL"),
    email: z.email("Invalid email format").optional(),
    phoneNumber: z.string().optional(),
    token: z.string(),
    code: z.string().optional(),
    expiresAt: z.coerce.date(),
    isVerified: z.boolean().default(false),
    verifiedAt: z.coerce.date().optional(),
    status: verificationStatusSchema.default("PENDING"),
    failureReason: z.string().optional(),
    attempts: z.number().int().min(0).default(0),
    maxAttempts: z.number().int().min(1).default(5),
    metadata: z.record(z.string(), z.any()).optional(),
    createdAt: z.coerce.date(),
    updatedAt: z.coerce.date(),
  })
  .strict();

/* --------------- Skema Autentikasi --------------- */
export const registerSchema = publicUserSchema
  .pick({
    email: true,
    firstName: true,
    lastName: true,
    phone: true,
  })
  .extend({
    password: passwordSchema,
    confirmPassword: z.string(),
    acceptTerms: z.literal(true, {
      message: "You must accept the terms and conditions",
    }),
    acceptPrivacy: z.literal(true, {
      message: "You must accept the terms and conditions",
    }),
  })
  .strict()
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

export const loginSchema = z
  .object({
    email: z.string().email(),
    password: z.string().min(1, "Password is required"),
    rememberMe: z.boolean().optional(),
  })
  .strict();

export const resetPasswordSchema = z
  .object({
    token: z.string(),
    password: passwordSchema,
    confirmPassword: z.string(),
  })
  .strict()
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

/* --------------- Skema Manajemen Pengguna --------------- */
export const userParamsSchema = z.object({
  id: z.cuid2(),
});

export const updateUserSchema = publicUserSchema
  .pick({
    firstName: true,
    lastName: true,
    phone: true,
  })
  .partial();

const userFilterSchema = z.object({
  search: z.string().optional(),
  isActive: z.coerce.boolean().optional(),
  role: userRoleSchema.optional(),
});

const paginationSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  sortBy: z.enum(["email", "firstName", "createdAt"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

export const getUsersSchema = paginationSchema.extend(userFilterSchema.shape);

/* --------------- Skema Verification --------------- */
export const createVerificationSchema = z
  .object({
    type: verificationTypeSchema,
    method: verificationMethodSchema.default("EMAIL"),
    email: z.string().email("Invalid email format").optional(),
    phoneNumber: z.string().optional(),
  })
  .strict()
  .refine(
    (data) => {
      if (data.method === "EMAIL" && !data.email) {
        return false;
      }
      if (
        (data.method === "SMS" || data.method === "WHATSAPP") &&
        !data.phoneNumber
      ) {
        return false;
      }
      return true;
    },
    {
      message:
        "Email is required for EMAIL method, phone number is required for SMS/WHATSAPP method",
    }
  );

export const verifyTokenSchema = z
  .object({
    token: z.string().min(1, "Token is required"),
    code: z.string().optional(),
  })
  .strict();

export const resendVerificationSchema = z
  .object({
    type: verificationTypeSchema,
    method: verificationMethodSchema.optional(),
  })
  .strict();

/* --------------- Skema Session --------------- */
export const createSessionSchema = z
  .object({
    deviceInfo: deviceInfoSchema.optional(),
    deviceFingerprint: z.string().optional(),
    ipAddress: z.string(),
    location: z.record(z.string(), z.any()).optional(),
    userAgent: z.string().optional(),
    loginMethod: z.string().optional(),
    isTrusted: z.boolean().default(false),
  })
  .strict();

export const sessionParamsSchema = z.object({
  sessionId: z.string().min(1, "Session ID is required"),
});

export const getUserSessionsSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(50).default(10),
  isActive: z.coerce.boolean().optional(),
});

/* --------------- Skema JWT --------------- */
export const jwtPayloadSchema = z.object({
  sub: z.string(),
  email: z.email(),
  role: userRoleSchema,
  iat: z.number(),
  exp: z.number(),
  jti: z.string().optional(),
});
