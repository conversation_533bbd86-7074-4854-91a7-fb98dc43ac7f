import { z } from "zod";

/* ------------------ Enums & Helpers ------------------ */
export const sessionStatusSchema = z.enum([
  "STARTING",
  "SCAN_QR_CODE",
  "WORKING",
  "STOPPED",
  "FAILED",
]);

const phoneSchema = z
  .string()
  .regex(/^(\+62|62|0)[0-9]{8,13}$/, "Invalid phone number");
const trimmedString = (schema: z.ZodString) =>
  z.preprocess((val) => (typeof val === "string" ? val.trim() : val), schema);

/* ------------------ Skema Entitas Utama ------------------ */
export const wahaSessionSchema = z
  .object({
    id: z.cuid2(),
    businessId: z.cuid2(),
    sessionName: trimmedString(z.string().min(1, "Session name is required")),
    phoneNumber: phoneSchema,
    qrCode: z.string().optional(),
    status: sessionStatusSchema.default("STOPPED"),
    isActive: z.boolean().default(true),
    createdAt: z.coerce.date(),
    updatedAt: z.coerce.date(),
  })
  .strict();

/* ------------------ Skema Operasi CRUD ------------------ */
export const createWahaSessionSchema = wahaSessionSchema.omit({
  id: true,
  qrCode: true,
  status: true,
  createdAt: true,
  updatedAt: true,
});

export const updateWahaSessionSchema = createWahaSessionSchema
  .pick({
    sessionName: true,
  })
  .partial();

export const wahaSessionParamsSchema = z.object({
  businessId: z.cuid2(),
});

/* ------------------ Skema Operasi Spesifik ------------------ */
export const startSessionSchema = z.object({
  sessionName: z.string().min(1),
});

export const stopSessionSchema = z.object({
  sessionName: z.string().min(1),
});

/* ------------------ WAHA API Message Schemas ------------------ */
export const wahaWebhookSchema = z.object({
  url: z.string().url(),
  events: z.array(z.string()),
  hmac: z
    .object({
      key: z.string(),
    })
    .optional(),
});

export const wahaMessageSchema = z.object({
  id: z.string(),
  timestamp: z.number(),
  from: z.string(),
  fromMe: z.boolean(),
  body: z.string(),
  hasMedia: z.boolean(),
  ack: z.number().optional(),
  vCards: z.array(z.any()).optional(),
  location: z
    .object({
      latitude: z.number(),
      longitude: z.number(),
      description: z.string().optional(),
    })
    .optional(),
});

export const wahaFileSchema = z.object({
  mimetype: z.string(),
  filename: z.string().optional(),
  url: z.string().url().optional(),
  data: z.string().optional(), // base64
});

/* ------------------ Send Message Schemas ------------------ */
export const sendTextSchema = z.object({
  session: z.string().optional(),
  chatId: z.string(),
  text: z.string(),
  linkPreview: z.boolean().optional(),
  linkPreviewHighQuality: z.boolean().optional(),
  reply_to: z.string().optional(),
  mentions: z.array(z.string()).optional(),
});

export const sendImageSchema = z.object({
  session: z.string().optional(),
  chatId: z.string(),
  file: wahaFileSchema,
  caption: z.string().optional(),
  reply_to: z.string().optional(),
});

export const sendFileSchema = z.object({
  session: z.string().optional(),
  chatId: z.string(),
  file: wahaFileSchema.extend({
    filename: z.string(), // required for files
  }),
  reply_to: z.string().optional(),
});

export const sendVoiceSchema = z.object({
  session: z.string().optional(),
  chatId: z.string(),
  file: wahaFileSchema,
  convert: z.boolean().optional(),
  reply_to: z.string().optional(),
});

export const sendLocationSchema = z.object({
  session: z.string().optional(),
  chatId: z.string(),
  latitude: z.number(),
  longitude: z.number(),
  title: z.string().optional(),
  reply_to: z.string().optional(),
});

export const sendSeenSchema = z.object({
  session: z.string().optional(),
  chatId: z.string(),
  messageIds: z.array(z.string()).optional(),
  participant: z.string().optional(), // for group messages
});
