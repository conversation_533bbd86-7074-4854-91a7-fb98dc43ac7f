import { z } from "zod";

export const subscriptionStatusSchema = z.enum([
  "ACTIVE",
  "INACTIVE",
  "CANCELLED",
  "TRIAL",
]);

export const userPackageUsageDetailSchema = z
  .object({
    total_businesses: z.number().int().default(0),
    total_products: z.number().int().default(0),
  })
  .strict();

export const userSubscriptionSchema = z
  .object({
    id: z.cuid2(),
    userId: z.cuid2(),
    packageId: z.cuid2(),
    status: subscriptionStatusSchema,
    startDate: z.coerce.date(),
    endDate: z.coerce.date().optional(),
  })
  .strict();

export const userPackageUsageSchema = z
  .object({
    id: z.cuid2(),
    userId: z.cuid2(),
    usage: userPackageUsageDetailSchema,
  })
  .strict();

// Skema operasional untuk Subscription
export const createSubscriptionSchema = z.object({
  userId: z.cuid2(),
  packageId: z.cuid2(),
});

export const cancelSubscriptionSchema = z.object({
  userId: z.cuid2(),
});
