import { z } from "zod";

/* ------------------ Helpers & Sub-Schemas ------------------ */
const trimmedString = (schema: z.ZodString) =>
  z.preprocess((val) => (typeof val === "string" ? val.trim() : val), schema);

// Skema untuk variasi produk (dalam field JSON)
export const productVariationSchema = z.object({
  name: z.string().min(1, "Variation name is required"),
  price: z.number().positive("Variation price must be positive"),
  stock: z
    .number()
    .int()
    .min(0, "Variation stock must be non-negative")
    .optional(),
  sku: z.string().optional(),
});

/* ------------------ Skema Entitas Utama ------------------ */
export const productSchema = z
  .object({
    id: z.cuid2(),
    businessId: z.cuid2(),
    name: trimmedString(z.string().min(1, "Product name is required").max(150)),
    description: z.string().max(2000).optional(),
    stock: z.number().int().min(0, "Stock must be non-negative").optional(),
    variations: z.array(productVariationSchema).optional(),
    price: z.number().positive("Price must be greater than zero"),
    category: z.string().optional(),
    image: z.string().url("Invalid image URL format").optional(),
    isActive: z.boolean().default(true),
    createdAt: z.coerce.date(),
    updatedAt: z.coerce.date(),
  })
  .strict();

/* ------------------ Skema Operasi CRUD ------------------ */
export const createProductSchema = productSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const updateProductSchema = createProductSchema.partial();

export const productParamsSchema = z.object({
  id: z.cuid2(),
});

export const deleteProductSchema = z.object({
  id: z.cuid2(),
});

/* ------------------ Skema GET (Daftar & Filter) ------------------ */
const paginationSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
});

const productFilterSchema = z.object({
  search: z.string().optional(),
  category: z.string().optional(),
  isActive: z.coerce.boolean().optional(),
});

const productSortSchema = z.object({
  sortBy: z.enum(["name", "price", "stock", "createdAt"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

export const getProductsSchema = paginationSchema
  .extend(productFilterSchema.shape)
  .extend(productSortSchema.shape);

/* ------------------ Skema Operasi Massal ------------------ */
export const bulkUpdateProductSchema = z.object({
  ids: z.array(z.cuid2()).min(1),
  data: updateProductSchema,
});

export const bulkDeleteProductSchema = z.object({
  ids: z.array(z.cuid2()).min(1),
});
