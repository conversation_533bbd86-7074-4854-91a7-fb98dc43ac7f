import { z } from "zod";

/* ------------------ <PERSON>ums & Helpers ------------------ */
export const businessTypeSchema = z.enum(["PRODUCT", "SERVICE", "BOTH"]);
const phoneSchema = z
  .string()
  .regex(/^(\+62|62|0)[0-9]{8,13}$/, "Invalid phone number");
const trimmedString = (schema: z.ZodString) =>
  z.preprocess((val) => (typeof val === "string" ? val.trim() : val), schema);

/* ------------------ Skema JSON untuk BusinessDetail ------------------ */
const dayHoursSchema = z
  .object({
    isOpen: z.boolean().default(false),
    openTime: z
      .string()
      .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .optional(),
    closeTime: z
      .string()
      .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .optional(),
  })
  .optional();

export const businessHoursSchema = z.object({
  monday: dayHoursSchema,
  tuesday: dayHoursSchema,
  wednesday: dayHoursSchema,
  thursday: dayHoursSchema,
  friday: dayHoursSchema,
  saturday: dayHoursSchema,
  sunday: dayHoursSchema,
});

export const socialProofSchema = z
  .object({
    testimonials: z
      .array(z.object({ name: z.string(), message: z.string() }))
      .optional(),
    awards: z.array(z.string()).optional(),
    certifications: z.array(z.string()).optional(),
  })
  .strict();

export const contactInfoSchema = z
  .object({
    whatsapp: phoneSchema.optional(),
    telegram: z.string().optional(),
    instagram: z.url().optional(),
    facebook: z.url().optional(),
    twitter: z.url().optional(),
    linkedin: z.url().optional(),
    youtube: z.url().optional(),
    tiktok: z.url().optional(),
    email: z.email().optional(),
    website: z.url().optional(),
  })
  .strict();

export const deliveryInfoSchema = z.object({
  selfPickup: z.boolean().optional().default(false),
  delivery: z.boolean().optional().default(false),
  shipping: z.boolean().optional().default(false),
  deliveryAreas: z
    .array(z.string().min(1, "Delivery area cannot be empty"))
    .optional(),
  estimatedDeliveryTime: z
    .string()
    .max(100, "Delivery time description too long")
    .optional(),
  deliveryFee: z
    .number()
    .min(0, "Delivery fee must be non-negative")
    .optional(),
  freeDeliveryMinimum: z
    .number()
    .min(0, "Free delivery minimum must be non-negative")
    .optional(),
});

export const paymentMethodsSchema = z.object({
  cash: z.boolean().optional().default(false),
  virtualAccount: z.boolean().optional().default(false),
  qris: z.boolean().optional().default(false),
  bankTransfer: z.boolean().optional().default(false),
  digitalWallet: z.boolean().optional().default(false),
  creditCard: z.boolean().optional().default(false),
  installment: z.boolean().optional().default(false),
  providers: z
    .object({
      tripay: z.boolean().optional().default(false),
      midtrans: z.boolean().optional().default(false),
      xendit: z.boolean().optional().default(false),
      doku: z.boolean().optional().default(false),
    })
    .optional(),
});

/* ------------------ Skema Entitas Utama ------------------ */
export const businessSchema = z
  .object({
    id: z.cuid2(),
    userId: z.cuid2(),
    name: trimmedString(
      z.string().min(1, "Business name is required").max(100)
    ),
    description: z.string().max(1000).optional(),
    phone: phoneSchema.optional(),
    email: z.email().optional(),
    address: z.string().max(500).optional(),
    website: z.url().optional(),
    logo: z.url().optional(),
    isActive: z.boolean().default(true),
    createdAt: z.coerce.date(),
    updatedAt: z.coerce.date(),
  })
  .strict();

export const businessDetailSchema = z
  .object({
    id: z.cuid2(),
    businessId: z.cuid2(),
    businessType: businessTypeSchema,
    businessHours: businessHoursSchema.optional(),
    mainBenefit: z.string().max(500).optional(),
    customerProblem: z.string().max(500).optional(),
    solution: z.string().max(500).optional(),
    targetAudience: z.string().max(500).optional(),
    promo: z.string().max(500).optional(),
    guarantee: z.string().max(500).optional(),
    uniqueSellingPoint: z.string().max(500).optional(),
    socialProof: socialProofSchema.optional(),
    contactInfo: contactInfoSchema.optional(),
    paymentMethods: paymentMethodsSchema.optional(),
    deliveryInfo: deliveryInfoSchema.optional(),
    isCompleted: z.boolean().default(false),
    createdAt: z.coerce.date(),
    updatedAt: z.coerce.date(),
  })
  .strict();

/* ------------------ Skema Operasi CRUD ------------------ */
export const createBusinessSchema = businessSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});
export const updateBusinessSchema = createBusinessSchema.partial();
export const businessParamsSchema = z.object({ id: z.cuid2() });
export const deleteBusinessSchema = z.object({ id: z.cuid2() });
export const updateBusinessDetailSchema = businessDetailSchema
  .omit({ id: true, businessId: true, createdAt: true, updatedAt: true })
  .partial();

/* ------------------ Skema GET (Daftar & Filter) ------------------ */
const paginationSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
});
const sortSchema = z.object({
  sortBy: z.string().default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});
const businessFilterSchema = z.object({
  search: z.string().optional(),
  isActive: z.coerce.boolean().optional(),
  userId: z.cuid2().optional(),
});
export const getBusinessesSchema = paginationSchema
  .extend(sortSchema.shape)
  .extend(businessFilterSchema.shape);

/* ------------------ Skema Operasi Massal ------------------ */
export const bulkUpdateBusinessSchema = z.object({
  // <-- ADA DI SINI
  ids: z.array(z.cuid2()).min(1),
  data: updateBusinessSchema,
});
export const bulkDeleteBusinessSchema = z.object({
  // <-- ADA DI SINI
  ids: z.array(z.cuid2()).min(1),
});
