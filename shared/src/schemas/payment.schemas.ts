import { z } from "zod";

/* ------------------ Enums & Helpers ------------------ */
export const paymentProviderSchema = z.enum([
  "TRIPAY",
  "MIDTRANS",
  "XENDIT",
  "MANUAL",
]);

export const paymentTransactionStatusSchema = z.enum([
  "PENDING",
  "PAID",
  "FAILED",
  "EXPIRED",
  "REFUNDED",
]);

/* ------------------ Skema Konfigurasi Gateway (Sangat Type-Safe) ------------------ */
// Definisikan konfigurasi spesifik untuk setiap provider
const tripayConfigSchema = z.object({
  provider: z.literal(paymentProviderSchema.enum.TRIPAY),
  apiKey: z.string().min(1),
  privateKey: z.string().min(1),
  merchantCode: z.string().min(1),
});

const midtransConfigSchema = z.object({
  provider: z.literal(paymentProviderSchema.enum.MIDTRANS),
  serverKey: z.string().min(1),
  clientKey: z.string().min(1),
});

// Gunakan discriminatedUnion untuk memastikan config sesuai dengan provider
export const gatewayConfigSchema = z.discriminatedUnion("provider", [
  tripayConfigSchema,
  midtransConfigSchema,
  // Tambahkan config provider lain di sini
]);

/* ------------------ Skema Entitas Utama ------------------ */
export const paymentGatewaySchema = z
  .object({
    id: z.cuid2(),
    businessId: z.cuid2(),
    provider: paymentProviderSchema,
    name: z.string().min(1),
    isActive: z.boolean().default(true),
    isDefault: z.boolean().default(false),
    config: gatewayConfigSchema,
    supportedMethods: z.array(z.string()).optional(),
    createdAt: z.coerce.date(),
    updatedAt: z.coerce.date(),
  })
  .strict();

export const paymentTransactionSchema = z
  .object({
    id: z.cuid2(),
    businessId: z.cuid2(),
    orderId: z.cuid2().optional(),
    appointmentId: z.cuid2().optional(),
    gatewayId: z.cuid2(),
    amount: z.number().positive(),
    status: paymentTransactionStatusSchema.default("PENDING"),
    paymentUrl: z.string().url().optional(),
    customerName: z.string(),
    customerPhone: z.string(),
    customerEmail: z.string().email().optional(),
    paidAt: z.coerce.date().optional(),
    expiredAt: z.coerce.date().optional(),
    createdAt: z.coerce.date(),
    updatedAt: z.coerce.date(),
  })
  .strict();

/* ------------------ Skema Operasi CRUD ------------------ */
export const createPaymentGatewaySchema = paymentGatewaySchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const updatePaymentGatewaySchema = createPaymentGatewaySchema.partial();

export const gatewayParamsSchema = z.object({ gatewayId: z.cuid2() });

/* ------------------ Skema Logika Bisnis ------------------ */
export const createTransactionSchema = z
  .object({
    businessId: z.cuid2(),
    orderId: z.cuid2().optional(),
    appointmentId: z.cuid2().optional(),
    amount: z.number().positive("Amount must be greater than zero"),
    customer: z.object({
      name: z.string(),
      phone: z.string(),
      email: z.string().email(),
    }),
  })
  .refine((data) => data.orderId || data.appointmentId, {
    message: "Either orderId or appointmentId must be provided",
  });

export const transactionParamsSchema = z.object({ transactionId: z.cuid2() });

// Skema untuk validasi notifikasi webhook dari provider pembayaran
export const paymentWebhookPayloadSchema = z.object({
  provider: z.string(), // Misal: "midtrans", "tripay"
  payload: z.any(), // Payload asli dari webhook, perlu diparsing lebih lanjut
});

/* ------------------ Tripay Specific Schemas ------------------ */
export const tripayChannelTypeSchema = z.enum([
  "Virtual Account",
  "Convenience Store",
  "E-Wallet",
  "Direct Debit",
  "QRIS",
]);

export const tripayTransactionStatusSchema = z.enum([
  "UNPAID",
  "PAID",
  "FAILED",
  "EXPIRED",
  "REFUND",
]);

export const tripayFeeSchema = z.object({
  flat: z.number(),
  percent: z.number(),
});

export const tripayChannelSchema = z.object({
  code: z.string(),
  name: z.string(),
  type: tripayChannelTypeSchema,
  fee_merchant: tripayFeeSchema,
  fee_customer: tripayFeeSchema,
  total_fee: tripayFeeSchema,
  minimum_fee: z.number(),
  maximum_fee: z.number(),
  icon_url: z.string(),
  active: z.boolean(),
});

export const tripayOrderItemSchema = z.object({
  sku: z.string().optional(),
  name: z.string(),
  price: z.number(),
  quantity: z.number(),
  product_url: z.string().optional(),
  image_url: z.string().optional(),
});

export const tripayInstructionSchema = z.object({
  title: z.string(),
  steps: z.array(z.string()),
});

export const tripayTransactionSchema = z.object({
  reference: z.string(),
  merchant_ref: z.string(),
  payment_selection_type: z.string(),
  payment_method: z.string(),
  payment_name: z.string(),
  customer_name: z.string(),
  customer_email: z.string(),
  customer_phone: z.string(),
  callback_url: z.string(),
  return_url: z.string(),
  amount: z.number(),
  fee_merchant: z.number(),
  fee_customer: z.number(),
  total_fee: z.number(),
  amount_received: z.number(),
  pay_code: z.string(),
  pay_url: z.string().optional(),
  checkout_url: z.string(),
  status: tripayTransactionStatusSchema,
  expired_time: z.number(),
  order_items: z.array(tripayOrderItemSchema),
  instructions: z.array(tripayInstructionSchema),
  qr_code: z.string().optional(),
  qr_url: z.string().optional(),
});

export const createTripayTransactionSchema = z.object({
  method: z.string(),
  merchant_ref: z.string(),
  amount: z.number(),
  customer_name: z.string(),
  customer_email: z.string(),
  customer_phone: z.string(),
  order_items: z.array(tripayOrderItemSchema),
  callback_url: z.string().optional(),
  return_url: z.string().optional(),
  expired_time: z.number().optional(),
});

export const tripayFeeCalculationSchema = z.object({
  fee_merchant: z.number(),
  fee_customer: z.number(),
  total_fee: z.number(),
});

export const tripayTransactionListParamsSchema = z.object({
  page: z.number().optional(),
  per_page: z.number().optional(),
  sort: z.enum(["asc", "desc"]).optional(),
  reference: z.string().optional(),
  merchant_ref: z.string().optional(),
  method: z.string().optional(),
  status: z.string().optional(),
});

export const tripayTransactionListResponseSchema = z.object({
  data: z.array(tripayTransactionSchema),
  pagination: z.object({
    current_page: z.number(),
    per_page: z.number(),
    total: z.number(),
    total_pages: z.number(),
  }),
});
