import { z } from "zod";
import {
  createBusinessSchema,
  updateBusinessDetailSchema,
} from "./business.schemas";
import {
  createBotProfileSchema,
  updateBotSettingSchema,
} from "./bot-config.schemas";
import { createProductSchema } from "./product.schemas";
import { createServiceSchema } from "./service.schemas";
import { createWahaSessionSchema } from "./waha.schemas";

/* ------------------ Skema Data untuk Setiap Langkah Onboarding ------------------ */
export const onboardingStep1DataSchema = createBusinessSchema
  .pick({
    name: true,
    description: true,
    phone: true,
    email: true,
    address: true,
    website: true,
    logo: true,
  })
  .strict();

// Step 2: Menggunakan skema pembaruan detail bisnis yang sudah ada.
export const onboardingStep2DataSchema = updateBusinessDetailSchema
  .pick({
    businessType: true,
    businessHours: true,
    socialProof: true,
    contactInfo: true,
    paymentMethods: true,
    deliveryInfo: true,
    promo: true,
    guarantee: true,
    uniqueSellingPoint: true,
    mainBenefit: true,
    customerProblem: true,
    solution: true,
    targetAudience: true,
  })
  .strict();

// Step 3: Gabungan dari beberapa skema yang sudah ada.
export const onboardingStep3DataSchema = z
  .object({
    botProfile: createBotProfileSchema.pick({ name: true }),
    botSetting: updateBotSettingSchema.pick({
      role: true,
      tone: true,
      userSalutation: true,
    }),
    initialProduct: createProductSchema.optional(),
    initialService: createServiceSchema.optional(),
  })
  .strict();

// Step 4: Menggunakan skema pembuatan sesi WAHA yang sudah ada.
export const onboardingStep4DataSchema = createWahaSessionSchema.pick({
  sessionName: true,
  phoneNumber: true,
});

/* ------------------ Skema Entitas Utama Onboarding ------------------ */
export const businessOnboardingSchema = z
  .object({
    id: z.cuid2(),
    businessId: z.cuid2(),
    isCompleted: z.boolean(),
    completedAt: z.coerce.date().optional(),
    currentStep: z.number().int(),

    // Terapkan skema data untuk setiap langkah
    step1Data: onboardingStep1DataSchema.optional(),
    step2Data: onboardingStep2DataSchema.optional(),
    step3Data: onboardingStep3DataSchema.optional(),
    step4Data: onboardingStep4DataSchema.optional(),

    createdAt: z.coerce.date(),
    updatedAt: z.coerce.date(),
  })
  .strict();

/* ------------------ Skema Operasional ------------------ */
// Skema untuk payload saat user menyimpan data di satu langkah
export const updateOnboardingStepSchema = z
  .object({
    businessId: z.cuid2(),
    step: z.number().int().min(1).max(4),
    data: z.union([
      onboardingStep1DataSchema,
      onboardingStep2DataSchema,
      onboardingStep3DataSchema,
      onboardingStep4DataSchema,
    ]),
  })
  .refine(
    (data) => {
      // Validasi lanjutan: pastikan data sesuai dengan nomor langkah
      if (data.step === 1)
        return onboardingStep1DataSchema.safeParse(data.data).success;
      if (data.step === 2)
        return onboardingStep2DataSchema.safeParse(data.data).success;
      if (data.step === 3)
        return onboardingStep3DataSchema.safeParse(data.data).success;
      if (data.step === 4)
        return onboardingStep4DataSchema.safeParse(data.data).success;
      return false;
    },
    {
      message: "Data does not match the specified step number",
      path: ["data"],
    }
  );
