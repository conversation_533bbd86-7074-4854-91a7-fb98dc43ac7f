import { z } from "zod";

/* ------------------ Enums ------------------ */
export const billingCycleSchema = z.enum(["MONTHLY", "YEARLY", "LIFETIME"]);
export const currencySchema = z.enum(["IDR", "USD", "EUR"]);
export const packageStatusSchema = z.enum([
  "DRAFT",
  "ACTIVE",
  "INACTIVE",
  "ARCHIVED",
]);

/* ------------------ Limits ------------------ */
export const packageLimitsSchema = z.object({
  maximal_business: z
    .number()
    .int()
    .min(0, "Maximal business must be non-negative")
    .optional(),
  products: z
    .number()
    .int()
    .min(0, "Products limit must be non-negative")
    .optional(),
  services: z
    .number()
    .int()
    .min(0, "Services limit must be non-negative")
    .optional(),
  orders_per_month: z
    .number()
    .int()
    .min(0, "Orders per month must be non-negative")
    .optional(),
  appointments_per_month: z
    .number()
    .int()
    .min(0, "Appointments per month must be non-negative")
    .optional(),
  faq: z.number().int().min(0, "FAQ limit must be non-negative").optional(),
  faq_with_attachment: z.boolean().optional(),
  messages_per_month: z
    .number()
    .int()
    .min(0, "Messages per month must be non-negative")
    .optional(),
  storage_mb: z
    .number()
    .int()
    .min(0, "Storage must be non-negative")
    .optional(),
  custom_features: z
    .array(z.string().min(1, "Feature name cannot be empty"))
    .optional(),
});

/* ------------------ Features ------------------ */
export const packageFeaturesSchema = z
  .object({
    ai_switch_model: z.boolean().optional(),
    ai_content_generation: z.boolean().optional(),
    get_business_info: z.boolean().optional(),
    search_products: z.boolean().optional(),
    search_services: z.boolean().optional(),
    check_stock: z.boolean().optional(),
    check_availability: z.boolean().optional(),
    create_appointment: z.boolean().optional(),
    create_order: z.boolean().optional(),
    reschedule_appointment: z.boolean().optional(),
    cancel_order: z.boolean().optional(),
    cancel_appointment: z.boolean().optional(),
    generate_payment_link: z.boolean().optional(),
    send_invoice: z.boolean().optional(),
    check_payment_status: z.boolean().optional(),
    advanced_analytics: z.boolean().optional(),
    priority_support: z.boolean().optional(),
    semantic_faq: z.boolean().optional(),
    ai_chatbot: z.boolean().optional(),
    custom_bot_profile: z.boolean().optional(),
  })
  .catchall(z.any());

/* ------------------ Discount Config ------------------ */
export const discountConfigSchema = z
  .object({
    yearly_discount_percentage: z
      .number()
      .min(0)
      .max(100, "Discount cannot exceed 100%")
      .optional(),
    renewal_discount_percentage: z
      .number()
      .min(0)
      .max(100, "Discount cannot exceed 100%")
      .optional(),
    early_bird_discount: z
      .number()
      .min(0)
      .max(100, "Discount cannot exceed 100%")
      .optional(),
    bulk_discount: z
      .object({
        min_quantity: z
          .number()
          .int()
          .min(1, "Minimum quantity must be at least 1"),
        discount_percentage: z
          .number()
          .min(0)
          .max(100, "Discount cannot exceed 100%"),
      })
      .optional(),
    seasonal_discount: z
      .object({
        start_date: z.coerce.date({ message: "Invalid start date format" }),
        end_date: z.coerce.date({ message: "Invalid end date format" }),
        discount_percentage: z
          .number()
          .min(0)
          .max(100, "Discount cannot exceed 100%"),
      })
      .optional(),
  })
  .catchall(z.any());

/* ------------------ Package Schema ------------------ */
export const packageSchema = z
  .object({
    id: z.cuid2({ message: "Invalid Package ID format" }),
    name: z.preprocess(
      (val) => (typeof val === "string" ? val.trim() : val),
      z
        .string()
        .min(1, "Package name is required")
        .max(100, "Package name too long")
    ),
    displayName: z.preprocess(
      (val) => (typeof val === "string" ? val.trim() : val),
      z
        .string()
        .min(1, "Display name is required")
        .max(150, "Display name too long")
    ),
    description: z.string().max(500, "Description too long").optional(),
    price: z.number().min(0, "Price must be non-negative"),
    currency: currencySchema,
    limits: packageLimitsSchema,
    features: packageFeaturesSchema.optional(),
    discountConfig: discountConfigSchema.optional(),
    isActive: z.boolean(),
    isDefault: z.boolean(),
    showInSalespage: z.boolean(),
    sortOrder: z.number().int().min(0, "Sort order must be non-negative"),
    billingCycle: billingCycleSchema,
    trialDays: z
      .number()
      .int()
      .min(0, "Trial days must be non-negative")
      .max(365, "Trial days cannot exceed 365"),

    createdAt: z.coerce.date({
      message: "Invalid created date format",
    }),

    updatedAt: z.coerce.date({
      message: "Invalid updated date format",
    }),
  })
  .strict()
  .refine(
    (data) => {
      if (data.showInSalespage && !data.isActive) {
        return false;
      }
      return true;
    },
    {
      message: "Package must be active to show in salespage",
      path: ["showInSalespage"],
    }
  );

/* --------------- Create Package Schema --------------- */
export const createPackageSchema = packageSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

/* --------------- Get Package Schema --------------- */
export const getPackageSchema = z.object({
  id: z.cuid2({ message: "Invalid Package ID format" }),
});

/* --------------- Get Packages Schema --------------- */
const paginationSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  sortBy: z
    .enum(["name", "displayName", "price", "createdAt"])
    .default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

const filterPackageSchema = z
  .object({
    search: z
      .string()
      .min(1, "Search query must be at least 1 character")
      .optional(),
    isActive: z.coerce.boolean().optional(),
    priceMin: z
      .number()
      .min(0, "Minimum price must be non-negative")
      .optional(),
    priceMax: z
      .number()
      .min(0, "Maximum price must be non-negative")
      .optional(),
  })
  .refine(
    (data) => {
      if (data.priceMin !== undefined && data.priceMax !== undefined) {
        return data.priceMax >= data.priceMin;
      }
      return true;
    },
    {
      message: "Maximum price cannot be less than minimum price",
      path: ["priceMax"],
    }
  );

export const getPackagesSchema = paginationSchema.extend(
  filterPackageSchema.shape
);

/* --------------- Update Package Schema --------------- */
export const updatePackageSchema = createPackageSchema.partial();

/* --------------- Bulk Update Package Schema --------------- */
export const bulkUpdatePackageSchema = z.object({
  ids: z.array(z.cuid2()).min(1, "At least one package ID is required"),
  data: updatePackageSchema,
});

/* --------------- Delete Package Schema --------------- */
export const deletePackageSchema = z.object({
  id: z.cuid2({ message: "Invalid Package ID format" }),
});

/* --------------- Bulk Delete Package Schema --------------- */
export const bulkDeletePackageSchema = z.object({
  ids: z.array(z.cuid2()).min(1, "At least one package ID is required"),
});

/* --------------- Salespage Package Schema --------------- */
export const salesPagePackageSchema = packageSchema.omit({
  isActive: true,
  isDefault: true,
  showInSalespage: true,
  sortOrder: true,
  billingCycle: true,
  createdAt: true,
  updatedAt: true,
});
