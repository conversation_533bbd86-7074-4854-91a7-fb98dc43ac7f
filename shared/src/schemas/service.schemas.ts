import { z } from "zod";

/* ------------------ Enums & Helpers ------------------ */
export const serviceTypeSchema = z.enum([
  "ON_DEMAND",
  "SUBSCRIPTION",
  "CONSULTATION",
]);
const trimmedString = (schema: z.ZodString) =>
  z.preprocess((val) => (typeof val === "string" ? val.trim() : val), schema);

/* ------------------ Skema Entitas Utama ------------------ */
export const serviceSchema = z
  .object({
    id: z.cuid2(),
    businessId: z.cuid2(),
    name: trimmedString(z.string().min(1, "Service name is required").max(150)),
    description: z.string().max(2000).optional(),
    price: z.number().positive("Price must be greater than zero"),
    // <PERSON><PERSON>i dalam menit
    duration: z
      .number()
      .int()
      .positive("Duration must be a positive integer")
      .optional(),
    serviceType: serviceTypeSchema.default("ON_DEMAND"),
    // Jeda antar layanan dalam menit
    delay: z
      .number()
      .int()
      .positive("Delay must be a positive integer")
      .optional(),
    category: z.string().optional(),
    isActive: z.boolean().default(true),
    createdAt: z.coerce.date(),
    updatedAt: z.coerce.date(),
  })
  .strict();

/* ------------------ Skema Operasi CRUD ------------------ */
export const createServiceSchema = serviceSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const updateServiceSchema = createServiceSchema.partial();

export const serviceParamsSchema = z.object({
  id: z.cuid2(),
});

/* ------------------ Skema GET (Daftar & Filter) ------------------ */
const paginationSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
});

const serviceFilterSchema = z.object({
  search: z.string().optional(),
  category: z.string().optional(),
  serviceType: serviceTypeSchema.optional(),
  isActive: z.coerce.boolean().optional(),
});

const serviceSortSchema = z.object({
  sortBy: z
    .enum(["name", "price", "duration", "createdAt"])
    .default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

export const getServicesSchema = paginationSchema
  .extend(serviceFilterSchema.shape)
  .extend(serviceSortSchema.shape);

/* ------------------ Skema Operasi Massal ------------------ */
export const bulkUpdateServiceSchema = z.object({
  ids: z.array(z.cuid2()).min(1),
  data: updateServiceSchema,
});

export const bulkDeleteServiceSchema = z.object({
  ids: z.array(z.cuid2()).min(1),
});
