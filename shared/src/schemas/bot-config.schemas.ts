import { z } from "zod";

/* ------------------ Enums & Helpers ------------------ */
export const botObjectiveSchema = z.enum([
  "SALES",
  "SUPPORT",
  "LEAD_GENERATION",
  "CUSTOMER_SUPPORT",
  "CLOSING",
  "APPOINTMENT",
]);
export const responseLengthSchema = z.enum(["concise", "detailed", "balanced"]);

const trimmedString = (schema: z.ZodString) =>
  z.preprocess((val) => (typeof val === "string" ? val.trim() : val), schema);

/* ------------------ Skema Entitas Utama ------------------ */
export const botProfileSchema = z
  .object({
    id: z.cuid2(),
    businessId: z.cuid2(),
    name: trimmedString(z.string().min(1, "Profile name is required").max(100)),
    description: z.string().max(500).optional(),
    isActive: z.boolean().default(true),
    createdAt: z.coerce.date(),
    updatedAt: z.coerce.date(),
  })
  .strict();

export const botSettingSchema = z
  .object({
    id: z.cuid2(),
    botProfileId: z.cuid2(),
    masterAiSwitch: z.boolean().default(true),
    botName: trimmedString(z.string().max(50)).optional(),
    role: trimmedString(z.string().min(1).max(100)),
    tone: trimmedString(z.string().min(1).max(200)),
    responseLength: responseLengthSchema.default("balanced"),
    userSalutation: trimmedString(z.string().min(1).max(20)),
    primaryLanguage: z.enum(["id", "en"]).default("id"),
    useEmoji: z.boolean().default(true),
    useTextStyling: z.boolean().default(true),
    enableRegionalDialect: z.boolean().default(false),
    dialectInstruction: trimmedString(z.string().max(200)).optional(),
    vocabularyOverride: z.record(z.string(), z.string()).optional(),
    botObjective: botObjectiveSchema,
    interactionStyle: z.enum(["proactive", "reactive"]).default("proactive"),
    empathyLevel: z.number().min(0).max(1),
    maxWordsPerReply: z.number().int().min(10).max(500),
    fallbackMessage: trimmedString(z.string().min(1).max(200)),
    pauseOnUserTyping: z.boolean().default(false),
    typingStyle: z.enum(["natural", "strict"]).default("natural"),
    typingSpeed: z.number().int().min(50).max(500),
    enableSendSeen: z.boolean().default(true),
    enableStartTyping: z.boolean().default(true),
    enableStopTyping: z.boolean().default(true),
    createdAt: z.coerce.date(),
    updatedAt: z.coerce.date(),
  })
  .strict();

export const botProfileTemplateSchema = z
  .object({
    id: z.cuid2(),
    name: z.string(),
    description: z.string(),
    category: z.string(),
    botConfig: botSettingSchema
      .omit({
        id: true,
        botProfileId: true,
        createdAt: true,
        updatedAt: true,
      })
      .partial(),
    isActive: z.boolean(),
  })
  .strict();

/* ------------------ Skema Operasi CRUD ------------------ */
export const createBotProfileSchema = botProfileSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});
export const updateBotProfileSchema = createBotProfileSchema.partial();
export const botProfileParamsSchema = z.object({ id: z.cuid2() });
export const getBotProfileByBusinessParamsSchema = z.object({
  businessId: z.cuid2(),
});

export const updateBotSettingSchema = botSettingSchema
  .omit({ id: true, botProfileId: true, createdAt: true, updatedAt: true })
  .partial();

/* ------------------ Skema Logika Bisnis ------------------ */
export const applyTemplateSchema = z.object({
  botProfileId: z.cuid2(),
  templateId: z.cuid2(),
});
