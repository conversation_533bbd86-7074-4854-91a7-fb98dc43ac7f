import { z } from "zod";

/* ------------------ Enums & Helpers ------------------ */
export const voucherTypeSchema = z.enum(["PERCENTAGE", "FIXED_AMOUNT"]);

// BEST PRACTICE: Buat skema yang dapat digunakan kembali untuk validasi kode voucher
const voucherCodeSchema = z.preprocess(
  (val) => (typeof val === "string" ? val.trim().toUpperCase() : val),
  z
    .string()
    .min(3, "Voucher code must be at least 3 characters")
    .max(20, "Voucher code too long")
    .regex(
      /^[A-Z0-9_-]+$/,
      "Code can only contain uppercase letters, numbers, hyphens, and underscores"
    )
);

/* ------------------ Skema Entitas Utama ------------------ */
export const voucherSchema = z
  .object({
    id: z.cuid2(),
    name: z.preprocess(
      (val) => (typeof val === "string" ? val.trim() : val),
      z.string().min(1)
    ),
    description: z.string().max(500).optional(),
    code: voucherCodeSchema, // Menggunakan skema yang dapat digunakan kembali
    type: voucherTypeSchema,
    // BEST PRACTICE: Gunakan .positive() untuk nilai > 0
    value: z.number().positive("Voucher value must be greater than 0"),
    minValue: z.number().min(0).optional(),
    maxValue: z.number().min(0).optional(),
    startDate: z.coerce.date(),
    endDate: z.coerce.date(),
    isActive: z.boolean(),
    isRedeemed: z.boolean(),
  })
  .strict()
  .refine((data) => (data.type === "PERCENTAGE" ? data.value <= 100 : true), {
    message: "Percentage voucher value cannot exceed 100%",
    path: ["value"],
  })
  .refine(
    (data) =>
      data.maxValue === undefined ||
      data.minValue === undefined ||
      data.maxValue >= data.minValue,
    {
      message: "Maximum value cannot be less than minimum value",
      path: ["maxValue"],
    }
  )
  .refine((data) => data.endDate > data.startDate, {
    message: "End date must be after start date",
    path: ["endDate"],
  });

/* ------------------ Skema Operasi CRUD ------------------ */
export const createVoucherSchema = voucherSchema.omit({
  id: true,
  isRedeemed: true,
});
export const updateVoucherSchema = createVoucherSchema.partial();
export const getVoucherSchema = z.object({ id: z.cuid2() });
export const deleteVoucherSchema = z.object({ id: z.cuid2() });

/* ------------------ Skema GET & Filter ------------------ */
export const getVoucherByCodeSchema = z.object({ code: voucherCodeSchema });

const paginationSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
});

const voucherFilterSchema = z.object({
  search: z.string().optional(),
  type: voucherTypeSchema.optional(),
  isActive: z.coerce.boolean().optional(),
});

const voucherSortSchema = z.object({
  sortBy: z
    .enum(["name", "value", "startDate", "endDate"])
    .default("startDate"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

export const getVouchersSchema = paginationSchema
  .extend(voucherFilterSchema.shape)
  .extend(voucherSortSchema.shape);

export const getActiveVouchersSchema = paginationSchema
  .pick({ page: true, limit: true })
  .extend(voucherFilterSchema.pick({ search: true }).shape);

/* ------------------ Skema Operasi Massal ------------------ */
export const bulkUpdateVoucherSchema = z.object({
  ids: z.array(z.cuid2()).min(1),
  data: updateVoucherSchema,
});
export const bulkDeleteVoucherSchema = z.object({
  ids: z.array(z.cuid2()).min(1),
});

/* ------------------ Skema Logika Bisnis ------------------ */
export const validateVoucherSchema = z.object({
  code: voucherCodeSchema,
  orderAmount: z.number().min(0).optional(),
});

export const redeemVoucherSchema = z.object({
  code: voucherCodeSchema,
  orderAmount: z.number().positive(),
});

/* ------------------ Skema DTO (Data Transfer Objects) ------------------ */
// BEST PRACTICE: Definisikan DTO sebagai skema Zod
export const voucherValidationResultSchema = z.object({
  isValid: z.boolean(),
  voucher: voucherSchema.optional(),
  discountAmount: z.number().optional(),
  finalAmount: z.number().optional(),
  errors: z.array(z.string()).optional(),
});

export const voucherRedemptionResultSchema = z.object({
  success: z.boolean(),
  voucher: voucherSchema,
  discountAmount: z.number(),
  finalAmount: z.number(),
  originalAmount: z.number(),
});

export const voucherStatsSchema = z.object({
  totalVouchers: z.number(),
  activeVouchers: z.number(),
  redeemedVouchers: z.number(),
  totalDiscountGiven: z.number(),
});

export const voucherUsageHistorySchema = z.object({
  voucherId: z.cuid2(),
  usedAt: z.coerce.date(),
  orderAmount: z.number(),
  discountAmount: z.number(),
  userId: z.cuid2().optional(),
  orderId: z.string().optional(),
});
