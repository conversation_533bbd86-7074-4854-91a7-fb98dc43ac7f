import * as z from "zod";
import type { Api<PERSON><PERSON>ponse, PaginatedResponse } from "./api";
import {
  // Enums
  botObjectiveSchema,
  responseLengthSchema,
  // Schemas
  botProfileSchema,
  botSettingSchema,
  botProfileTemplateSchema,
  createBotProfileSchema,
  updateBotProfileSchema,
  botProfileParamsSchema,
  getBotProfileByBusinessParamsSchema,
  updateBotSettingSchema,
  applyTemplateSchema,
} from "../schemas/bot-config.schemas";

/* ------------------ Tipe Dasar & Enum ------------------ */
export type BotObjective = z.infer<typeof botObjectiveSchema>;
export type ResponseLength = z.infer<typeof responseLengthSchema>;

/* ------------------ Tipe Entitas & DTO ------------------ */
export type BotProfile = z.infer<typeof botProfileSchema>;
export type BotSetting = z.infer<typeof botSettingSchema>;
export type BotProfileTemplate = z.infer<typeof botProfileTemplateSchema>;

// DTO Gabungan untuk menampilkan profil beserta setting-nya
export interface BotProfileWithSettings extends BotProfile {
  botSetting?: BotSetting;
}

/* ------------------ Tipe Payload, Params & Query --------------- */
export type CreateBotProfilePayload = z.infer<typeof createBotProfileSchema>;
export type UpdateBotProfilePayload = z.infer<typeof updateBotProfileSchema>;
export type BotProfileParams = z.infer<typeof botProfileParamsSchema>;
export type GetBotProfileByBusinessParams = z.infer<
  typeof getBotProfileByBusinessParamsSchema
>;
export type UpdateBotSettingPayload = z.infer<typeof updateBotSettingSchema>;
export type ApplyTemplatePayload = z.infer<typeof applyTemplateSchema>;

/* ------------------ Tipe Respons API --------------- */
export type CreateBotProfileResponse = ApiResponse<BotProfile>;
export type GetBotProfileResponse = ApiResponse<BotProfileWithSettings>; // Mengembalikan data lengkap
export type UpdateBotProfileResponse = ApiResponse<BotProfile>;
export type DeleteBotProfileResponse = ApiResponse<null>;

export type GetBotSettingResponse = ApiResponse<BotSetting>;
export type UpdateBotSettingResponse = ApiResponse<BotSetting>;

export type ApplyTemplateResponse = ApiResponse<BotSetting>;

export type GetBotProfileTemplatesResponse =
  PaginatedResponse<BotProfileTemplate>;
