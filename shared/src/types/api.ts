// Base API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
  meta?: ResponseMeta;
}

export interface ResponseMeta {
  timestamp: string;
  requestId?: string;
  version?: string;
}

// Pagination Types
export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface PaginationMeta {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  meta: ResponseMeta & {
    pagination: PaginationMeta;
  };
}

// HTTP Status Codes
export enum HttpStatusCode {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  INTERNAL_SERVER_ERROR = 500,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
}

// Error Types
export interface ApiError {
  code: string;
  message: string;
  field?: string;
  details?: Record<string, any>;
}

export interface ValidationError extends ApiError {
  field: string;
  value?: any;
  constraints?: string[];
}

// Request Types
export interface BaseRequest {
  headers?: Record<string, string>;
  params?: Record<string, string | number>;
  query?: Record<string, any>;
}

export interface CreateRequest<T> extends BaseRequest {
  body: Omit<T, "id" | "createdAt" | "updatedAt">;
}

export interface UpdateRequest<T> extends BaseRequest {
  body: Partial<Omit<T, "id" | "createdAt" | "updatedAt">>;
}

export interface DeleteRequest extends BaseRequest {
  id: string | number;
}

// Search and Filter Types
export interface SearchParams {
  q?: string;
  filters?: Record<string, any>;
  sort?: SortParams[];
}

export interface SortParams {
  field: string;
  order: "asc" | "desc";
}

export interface FilterParams {
  field: string;
  operator:
    | "eq"
    | "ne"
    | "gt"
    | "gte"
    | "lt"
    | "lte"
    | "in"
    | "nin"
    | "like"
    | "between";

  value: any;
}

// File Upload Types
export interface FileUploadResponse {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  uploadedAt: string;
}

export interface FileUploadRequest {
  file: File | Buffer;
  folder?: string;
  metadata?: Record<string, any>;
}

// Batch Operations

export interface BatchRequest<T> {
  items: T[];
  options?: {
    skipErrors?: boolean;
    validateAll?: boolean;
  };
}

export interface BatchResponse<T> {
  success: boolean;
  results: BatchResult<T>[];
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

export interface BatchResult<T> {
  success: boolean;
  data?: T;
  error?: ApiError;
  index: number;
}

// API Endpoint Types

export interface ApiEndpoint {
  method: "GET" | "POST" | "PUT" | "PATCH" | "DELETE";
  path: string;
  description?: string;
  requiresAuth?: boolean;
  permissions?: string[];
}

// Rate Limiting

export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
  retryAfter?: number;
}

// Health Check

export interface HealthCheckResponse {
  status: "healthy" | "unhealthy" | "degraded";
  timestamp: string;
  uptime: number;
  version: string;
  services?: Record<string, ServiceHealth>;
}

export interface ServiceHealth {
  status: "up" | "down" | "degraded";
  responseTime?: number;
  lastCheck: string;
  error?: string;
}

// Generic CRUD Operations
export type CrudOperation = "create" | "read" | "update" | "delete" | "list";

export interface CrudResponse<T> {
  operation: CrudOperation;
  resource: string;
  data?: T;
  success: boolean;
  message?: string;
}

// API Configuration

export interface ApiConfig {
  baseUrl: string;
  timeout: number;
  retries: number;
  headers: Record<string, string>;
  interceptors?: {
    request?: Function[];
    response?: Function[];
  };
}

// API Constants
export const API_CONSTANTS = {
  VERSION: "1.0.0",
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
  DEFAULT_TIMEOUT: 30000, // 30 seconds
} as const;

// JWT Constants
export const JWT_CONSTANTS = {
  ISSUER: "takono-api",
  AUDIENCE: "takono-client",
  TOKEN_TYPES: {
    ACCESS: "access",
    REFRESH: "refresh",
  },
} as const;

// User Selection Constants
export const USER_SELECT_FIELDS = {
  PUBLIC: {
    id: true,
    email: true,
    firstName: true,
    lastName: true,
    role: true,
    phone: true,
    isActive: true,
    isEmailVerified: true,
    createdAt: true,
    updatedAt: true,
  },
  MINIMAL: {
    id: true,
    email: true,
    firstName: true,
    lastName: true,
    role: true,
  },
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  UNAUTHORIZED: "Unauthorized access",
  FORBIDDEN: "Access forbidden",
  NOT_FOUND: "Resource not found",
  VALIDATION_FAILED: "Validation failed",
  INTERNAL_ERROR: "Internal server error",
  TOKEN_REQUIRED: "Access token required",
  TOKEN_INVALID: "Invalid token",
  SESSION_REQUIRED: "Session required",
  SESSION_INVALID: "Invalid session",
  SESSION_EXPIRED: "Session expired",
  USER_NOT_FOUND: "User not found",
  ACCOUNT_DEACTIVATED: "Account is deactivated",
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  CREATED: "Resource created successfully",
  UPDATED: "Resource updated successfully",
  DELETED: "Resource deleted successfully",
  LOGIN_SUCCESS: "Login successful",
  LOGOUT_SUCCESS: "Logout successful",
  REGISTRATION_SUCCESS: "Registration successful",
  VERIFICATION_SENT: "Verification code sent successfully",
  VERIFICATION_SUCCESS: "Verification completed successfully",
} as const;
