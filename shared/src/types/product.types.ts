import * as z from "zod";
import type { ApiR<PERSON>ponse, PaginatedResponse } from "./api";
import {
  // Impor semua skema yang dibutuhkan
  productVariationSchema,
  productSchema,
  createProductSchema,
  updateProductSchema,
  productParamsSchema,
  getProductsSchema,
  bulkUpdateProductSchema,
  bulkDeleteProductSchema,
} from "../schemas/product.schemas";

/* --------------- Tipe Dasar & Entitas --------------- */
export type ProductVariation = z.infer<typeof productVariationSchema>;
export type Product = z.infer<typeof productSchema>;

/* --------------- Tipe Payload, Params & Query --------------- */
export type CreateProductPayload = z.infer<typeof createProductSchema>;
export type UpdateProductPayload = z.infer<typeof updateProductSchema>;
export type ProductParams = z.infer<typeof productParamsSchema>;
export type GetProductsQuery = z.infer<typeof getProductsSchema>;
export type BulkUpdateProductPayload = z.infer<typeof bulkUpdateProductSchema>;
export type BulkDeleteProductPayload = z.infer<typeof bulkDeleteProductSchema>;

/* --------------- Tipe Respons API --------------- */
export type CreateProductResponse = ApiResponse<Product>;
export type GetProductResponse = ApiResponse<Product>;
export type GetProductsResponse = PaginatedResponse<Product>;
export type UpdateProductResponse = ApiResponse<Product>;
export type DeleteProductResponse = ApiResponse<null>;
export type BulkUpdateProductResponse = ApiResponse<{ count: number }>;
export type BulkDeleteProductResponse = ApiResponse<{ count: number }>;
