import * as z from "zod";
import type { ApiResponse } from "./api";
import type { Package, PackageLimit } from "./package.types";
import {
  subscriptionStatusSchema,
  userPackageUsageDetailSchema,
  userSubscriptionSchema,
  userPackageUsageSchema,
  createSubscriptionSchema,
  cancelSubscriptionSchema,
} from "../schemas/subcription.schemas";

export type SubscriptionStatus = z.infer<typeof subscriptionStatusSchema>;
export type UserPackageUsageDetail = z.infer<
  typeof userPackageUsageDetailSchema
>;

export type UserSubscription = z.infer<typeof userSubscriptionSchema>;
export type UserPackageUsage = z.infer<typeof userPackageUsageSchema>;

export interface SubscriptionWithPackage extends UserSubscription {
  package: Package;
}
export interface UserUsageWithLimits {
  usage: UserPackageUsage;
  limits: PackageLimit;
}

export type CreateSubscriptionPayload = z.infer<
  typeof createSubscriptionSchema
>;
export type CancelSubscriptionPayload = z.infer<
  typeof cancelSubscriptionSchema
>;

export type CreateSubscriptionResponse = ApiResponse<UserSubscription>;
export type GetSubscriptionResponse =
  ApiResponse<SubscriptionWithPackage | null>;
export type CancelSubscriptionResponse = ApiResponse<UserSubscription>;
export type GetUserUsageResponse = ApiResponse<UserUsageWithLimits | null>;
