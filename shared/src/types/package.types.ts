// types/package.ts

import * as z from "zod";
import {
  billingCycleSchema,
  currencySchema,
  packageStatusSchema,
  packageLimitsSchema,
  packageFeaturesSchema,
  discountConfigSchema,
  packageSchema,
  createPackageSchema,
  getPackageSchema,
  updatePackageSchema,
  getPackagesSchema,
  bulkUpdatePackageSchema,
  bulkDeletePackageSchema,
  salesPagePackageSchema,
} from "../schemas/package.schemas";

import type { ApiResponse, PaginatedResponse } from "./api";

/* --------------- Tipe Dasar --------------- */
export type BillingCycle = z.infer<typeof billingCycleSchema>;
export type Currency = z.infer<typeof currencySchema>;
export type PackageStatus = z.infer<typeof packageStatusSchema>;
export type PackageLimit = z.infer<typeof packageLimitsSchema>;
export type PackageFeature = z.infer<typeof packageFeaturesSchema>;
export type DiscountConfig = z.infer<typeof discountConfigSchema>;

/* --------------- Type Entitas Utama --------------- */
export type Package = z.infer<typeof packageSchema>;

/* --------------- Type Payload & Query --------------- */
export type SalesPagePackage = z.infer<typeof salesPagePackageSchema>;
export type CreatePackagePayload = z.infer<typeof createPackageSchema>;
export type UpdatePackagePayload = z.infer<typeof updatePackageSchema>;
export type GetPackageParams = z.infer<typeof getPackageSchema>;
export type GetPackagesQuery = z.infer<typeof getPackagesSchema>;
export type BulkUpdatePackagePayload = z.infer<typeof bulkUpdatePackageSchema>;
export type BulkDeletePackagePayload = z.infer<typeof bulkDeletePackageSchema>;

/* --------------- Type Respons API --------------- */
export type GetSalesPagePackagesResponse = PaginatedResponse<SalesPagePackage>;
export type CreatePackageResponse = ApiResponse<Package>;
export type GetPackageResponse = ApiResponse<Package>;
export type GetPackagesResponse = PaginatedResponse<Package>;
export type UpdatePackageResponse = ApiResponse<Package>;
export type DeletePackageResponse = ApiResponse<null>;
export type BulkUpdatePackageResponse = ApiResponse<{ count: number }>;
export type BulkDeletePackageResponse = ApiResponse<{ count: number }>;
