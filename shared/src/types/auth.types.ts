import * as z from "zod";
import type { ApiR<PERSON>ponse, PaginatedResponse } from "./api";
import {
  // Enums
  verificationTypeSchema,
  verificationMethodSchema,
  verificationStatusSchema,
  userRoleSchema,
  // Schemas
  publicUserSchema,
  userSessionSchema,
  userSecuritySchema,
  userVerificationSchema,
  registerSchema,
  loginSchema,
  resetPasswordSchema,
  updateUserSchema,
  userParamsSchema,
  getUsersSchema,
  jwtPayloadSchema,
  // Verification schemas
  createVerificationSchema,
  verifyTokenSchema,
  resendVerificationSchema,
  // Session schemas
  createSessionSchema,
  sessionParamsSchema,
  getUserSessionsSchema,
} from "../schemas/auth.schemas";

/* --------------- Tipe Dasar & Enum --------------- */
export type VerificationType = z.infer<typeof verificationTypeSchema>;
export type VerificationMethod = z.infer<typeof verificationMethodSchema>;
export type VerificationStatus = z.infer<typeof verificationStatusSchema>;
export type UserRole = z.infer<typeof userRoleSchema>;

/* --------------- Tipe Entitas & Payload --------------- */
// Tipe untuk data user yang aman dikirim ke client
export type PublicUser = z.infer<typeof publicUserSchema>;
export type UserSession = z.infer<typeof userSessionSchema>;
export type UserSecurity = z.infer<typeof userSecuritySchema>;
export type UserVerification = z.infer<typeof userVerificationSchema>;

// Payloads
export type RegisterPayload = z.infer<typeof registerSchema>;
export type LoginPayload = z.infer<typeof loginSchema>;
export type ResetPasswordPayload = z.infer<typeof resetPasswordSchema>;
export type UpdateUserPayload = z.infer<typeof updateUserSchema>;

// Params & Queries
export type UserParams = z.infer<typeof userParamsSchema>;
export type GetUsersQuery = z.infer<typeof getUsersSchema>;

// Verification types
export type CreateVerificationPayload = z.infer<
  typeof createVerificationSchema
>;
export type VerifyTokenPayload = z.infer<typeof verifyTokenSchema>;
export type ResendVerificationPayload = z.infer<
  typeof resendVerificationSchema
>;

// Session types
export type CreateSessionPayload = z.infer<typeof createSessionSchema>;
export type SessionParams = z.infer<typeof sessionParamsSchema>;
export type GetUserSessionsQuery = z.infer<typeof getUserSessionsSchema>;

/* --------------- Tipe JWT --------------- */
export type JWTPayload = z.infer<typeof jwtPayloadSchema>;

/* --------------- Tipe DTO (Data Transfer Objects) --------------- */
// Tipe ini adalah gabungan data untuk ditampilkan di halaman profil
export interface UserProfile {
  id: string;
  email: string;
  firstName?: string | null;
  lastName?: string | null;
  phone?: string | null;
  role: UserRole;
  isEmailVerified: boolean;
  createdAt: Date;
}

/* --------------- Tipe Respons API --------------- */
export type LoginResponse = ApiResponse<{
  accessToken: string;
  user: PublicUser;
}>;

export type RegisterResponse = ApiResponse<{
  user: PublicUser;
  accessToken: string;
  message: string;
}>;

export type ResetPasswordResponse = ApiResponse<{
  message: string;
}>;

export type VerificationResponse = ApiResponse<{
  verification: UserVerification;
  message: string;
}>;

export type VerifyTokenResponse = ApiResponse<{
  message: string;
  isVerified: boolean;
}>;

export type SessionResponse = ApiResponse<{
  session: UserSession;
}>;

export type SessionsResponse = PaginatedResponse<UserSession>;

// Respons untuk endpoint GET /api/auth/me
export type GetMyProfileResponse = ApiResponse<UserProfile>;

// Respons untuk endpoint manajemen pengguna
export type GetUserResponse = ApiResponse<PublicUser>;
export type GetUsersResponse = PaginatedResponse<PublicUser>;
export type UpdateUserResponse = ApiResponse<PublicUser>;
export type DeleteUserResponse = ApiResponse<null>;
