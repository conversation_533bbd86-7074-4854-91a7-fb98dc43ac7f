import * as z from "zod";
import type { ApiResponse } from "./api";
import {
  onboardingStep1DataSchema,
  onboardingStep2DataSchema,
  onboardingStep3DataSchema,
  onboardingStep4DataSchema,
  businessOnboardingSchema,
  updateOnboardingStepSchema,
} from "../schemas/onboarding.schemas";

/* --------------- Tipe Data per Langkah --------------- */
export type OnboardingStep1Data = z.infer<typeof onboardingStep1DataSchema>;
export type OnboardingStep2Data = z.infer<typeof onboardingStep2DataSchema>;
export type OnboardingStep3Data = z.infer<typeof onboardingStep3DataSchema>;
export type OnboardingStep4Data = z.infer<typeof onboardingStep4DataSchema>;

/* --------------- Tipe Entitas Utama --------------- */
export type BusinessOnboarding = z.infer<typeof businessOnboardingSchema>;

/* --------------- Tipe Payload --------------- */
export type UpdateOnboardingStepPayload = z.infer<
  typeof updateOnboardingStepSchema
>;

/* --------------- Tipe Respons API --------------- */
export type GetOnboardingStatusResponse = ApiResponse<BusinessOnboarding>;
export type UpdateOnboardingStepResponse = ApiResponse<BusinessOnboarding>;
export type CompleteOnboardingResponse = ApiResponse<{
  isCompleted: boolean;
  completedAt: Date;
}>;
