import * as z from "zod";
import type { ApiResponse, PaginatedResponse } from "./api";
import {
  // Impor semua skema dari barrel file untuk kemudahan
  businessTypeSchema,
  businessHoursSchema,
  socialProofSchema,
  contactInfoSchema,
  businessSchema,
  businessDetailSchema,
  createBusinessSchema,
  updateBusinessSchema,
  businessParamsSchema,
  getBusinessesSchema,
  updateBusinessDetailSchema,
  bulkUpdateBusinessSchema,
  bulkDeleteBusinessSchema,
} from "../schemas/business.schemas";

/* --------------- Tipe Dasar & Enum --------------- */
export type BusinessType = z.infer<typeof businessTypeSchema>;
export type BusinessHours = z.infer<typeof businessHoursSchema>;
export type SocialProof = z.infer<typeof socialProofSchema>;
export type ContactInfo = z.infer<typeof contactInfoSchema>;

/* --------------- Tipe Entitas & DTO --------------- */
export type Business = z.infer<typeof businessSchema>;
export type BusinessDetail = z.infer<typeof businessDetailSchema>;

// DTO Gabungan untuk respons API yang lebih kaya
export interface BusinessWithDetails extends Business {
  businessDetail?: BusinessDetail;
}

/* --------------- Tipe Payload, Params & Query --------------- */
export type CreateBusinessPayload = z.infer<typeof createBusinessSchema>;
export type UpdateBusinessPayload = z.infer<typeof updateBusinessSchema>;
export type BusinessParams = z.infer<typeof businessParamsSchema>;
export type GetBusinessesQuery = z.infer<typeof getBusinessesSchema>;
export type UpdateBusinessDetailPayload = z.infer<
  typeof updateBusinessDetailSchema
>;

export type BulkUpdateBusinessPayload = z.infer<
  typeof bulkUpdateBusinessSchema
>;
export type BulkDeleteBusinessPayload = z.infer<
  typeof bulkDeleteBusinessSchema
>;

/* --------------- Tipe Respons API --------------- */
export type CreateBusinessResponse = ApiResponse<Business>;
export type GetBusinessResponse = ApiResponse<BusinessWithDetails>;
export type GetBusinessesResponse = PaginatedResponse<Business>;
export type UpdateBusinessResponse = ApiResponse<Business>;
export type DeleteBusinessResponse = ApiResponse<null>;
export type UpdateBusinessDetailResponse = ApiResponse<BusinessDetail>;
export type BulkUpdateBusinessResponse = ApiResponse<{ count: number }>;
export type BulkDeleteBusinessResponse = ApiResponse<{ count: number }>;
