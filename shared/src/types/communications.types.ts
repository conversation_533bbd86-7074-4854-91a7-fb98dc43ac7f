/* --------------- Base Communication Types --------------- */
export interface BaseAddress {
  name?: string;
}

export interface EmailAddress extends BaseAddress {
  email: string;
}

export interface PhoneAddress extends BaseAddress {
  phone: string;
  countryCode?: string;
}

export interface WhatsAppAddress extends BaseAddress {
  phone: string;
  countryCode?: string;
}

/* --------------- Email Types --------------- */
export interface EmailAttachment {
  filename: string;
  content: string; // base64 encoded
  contentType: string;
  disposition?: "attachment" | "inline";
  contentId?: string;
}

export interface SendEmailRequest {
  to: EmailAddress | EmailAddress[];
  cc?: EmailAddress | EmailAddress[];
  bcc?: EmailAddress | EmailAddress[];
  subject: string;
  html?: string;
  text?: string;
  attachments?: EmailAttachment[];
  replyTo?: EmailAddress;
  tags?: Record<string, string>;
  templateId?: string;
  templateData?: Record<string, any>;
}

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  html: string;
  text?: string;
  variables: string[];
}

export interface EmailResponse {
  messageId: string;
  success: boolean;
  provider?: string;
  timestamp?: string;
}

/* --------------- SMS Types --------------- */
export interface SendSMSRequest {
  to: PhoneAddress | PhoneAddress[];
  message: string;
  templateId?: string;
  templateData?: Record<string, any>;
  tags?: Record<string, string>;
}

export interface SMSResponse {
  messageId: string;
  success: boolean;
  provider?: string;
  timestamp?: string;
}

/* --------------- WhatsApp Types --------------- */
export interface SendWhatsAppRequest {
  to: WhatsAppAddress | WhatsAppAddress[];
  message?: string;
  templateId?: string;
  templateData?: Record<string, any>;
  mediaUrl?: string;
  mediaType?: "image" | "video" | "audio" | "document";
  tags?: Record<string, string>;
}

export interface WhatsAppResponse {
  messageId: string;
  success: boolean;
  provider?: string;
  timestamp?: string;
}

/* --------------- Push Notification Types --------------- */
export interface PushNotificationRequest {
  to: string | string[]; // device tokens
  title: string;
  body: string;
  data?: Record<string, any>;
  imageUrl?: string;
  sound?: string;
  badge?: number;
  tags?: Record<string, string>;
}

export interface PushNotificationResponse {
  messageId: string;
  success: boolean;
  provider?: string;
  timestamp?: string;
}

/* --------------- Provider Interface Types --------------- */
export interface EmailProvider {
  name: string;
  sendEmail(request: SendEmailRequest): Promise<EmailResponse>;
  sendTemplatedEmail(
    templateId: string,
    to: EmailAddress | EmailAddress[],
    templateData: Record<string, any>,
    options?: {
      cc?: EmailAddress | EmailAddress[];
      bcc?: EmailAddress | EmailAddress[];
      replyTo?: EmailAddress;
      tags?: Record<string, string>;
    }
  ): Promise<EmailResponse>;
  verifyEmailAddress?(email: string): Promise<{ success: boolean }>;
  getSendingStatistics?(): Promise<{
    bounces: number;
    complaints: number;
    deliveries: number;
    rejects: number;
  }>;
}

export interface SMSProvider {
  name: string;
  sendSMS(request: SendSMSRequest): Promise<SMSResponse>;
  sendTemplatedSMS?(
    templateId: string,
    to: PhoneAddress | PhoneAddress[],
    templateData: Record<string, any>,
    options?: {
      tags?: Record<string, string>;
    }
  ): Promise<SMSResponse>;
}

export interface WhatsAppProvider {
  name: string;
  sendMessage(request: SendWhatsAppRequest): Promise<WhatsAppResponse>;
  sendTemplatedMessage?(
    templateId: string,
    to: WhatsAppAddress | WhatsAppAddress[],
    templateData: Record<string, any>,
    options?: {
      tags?: Record<string, string>;
    }
  ): Promise<WhatsAppResponse>;
}

export interface PushNotificationProvider {
  name: string;
  sendNotification(request: PushNotificationRequest): Promise<PushNotificationResponse>;
}

/* --------------- Template Types --------------- */
export interface VerificationEmailData {
  userName: string;
  verificationCode: string;
  verificationUrl: string;
  expiresInMinutes: number;
}

export interface PasswordResetEmailData {
  userName: string;
  resetUrl: string;
  expiresInMinutes: number;
}

export interface WelcomeEmailData {
  userName: string;
  loginUrl: string;
  supportEmail: string;
}

/* --------------- Service Configuration Types --------------- */
export interface CommunicationConfig {
  email: {
    provider: "ses" | "sendgrid" | "smtp";
    defaultFrom: EmailAddress;
    templates: {
      verification: string;
      passwordReset: string;
      welcome: string;
    };
  };
  sms: {
    provider: "twilio" | "aws-sns";
    defaultFrom?: string;
    templates: {
      verification: string;
      passwordReset: string;
    };
  };
  whatsapp: {
    provider: "waha" | "twilio";
    templates: {
      verification: string;
      passwordReset: string;
    };
  };
  push: {
    provider: "fcm" | "apns";
  };
}
