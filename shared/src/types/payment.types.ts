import * as z from "zod";
import type { ApiResponse, PaginatedResponse } from "./api";
import {
  // Enums
  paymentProviderSchema,
  paymentTransactionStatusSchema,

  // Schemas
  gatewayConfigSchema,
  paymentGatewaySchema,
  paymentTransactionSchema,
  createPaymentGatewaySchema,
  updatePaymentGatewaySchema,
  gatewayParamsSchema,
  createTransactionSchema,
  transactionParamsSchema,
  paymentWebhookPayloadSchema,

  // Tripay Specific Schemas
  tripayChannelTypeSchema,
  tripayTransactionStatusSchema,
  tripayFeeSchema,
  tripayChannelSchema,
  tripayOrderItemSchema,
  tripayInstructionSchema,
  tripayTransactionSchema,
  createTripayTransactionSchema,
  tripayFeeCalculationSchema,
  tripayTransactionListParamsSchema,
  tripayTransactionListResponseSchema,
} from "../schemas/payment.schemas";

/* --------------- Tip<PERSON> & Enum --------------- */
export type PaymentProvider = z.infer<typeof paymentProviderSchema>;
export type PaymentTransactionStatus = z.infer<
  typeof paymentTransactionStatusSchema
>;
export type GatewayConfig = z.infer<typeof gatewayConfigSchema>;

/* --------------- Tipe Entitas & DTO --------------- */
export type PaymentGateway = z.infer<typeof paymentGatewaySchema>;
export type PaymentTransaction = z.infer<typeof paymentTransactionSchema>;

// DTO untuk menampilkan transaksi beserta detail gateway-nya
export interface TransactionWithGateway extends PaymentTransaction {
  gateway: PaymentGateway;
}

/* --------------- Tipe Payload, Params & Query --------------- */
export type CreatePaymentGatewayPayload = z.infer<
  typeof createPaymentGatewaySchema
>;
export type UpdatePaymentGatewayPayload = z.infer<
  typeof updatePaymentGatewaySchema
>;
export type GatewayParams = z.infer<typeof gatewayParamsSchema>;

export type CreateTransactionPayload = z.infer<typeof createTransactionSchema>;
export type TransactionParams = z.infer<typeof transactionParamsSchema>;
export type PaymentWebhookPayload = z.infer<typeof paymentWebhookPayloadSchema>;

/* --------------- Tipe Respons API --------------- */
export type CreatePaymentGatewayResponse = ApiResponse<PaymentGateway>;
export type GetPaymentGatewaysResponse = ApiResponse<PaymentGateway[]>;
export type UpdatePaymentGatewayResponse = ApiResponse<PaymentGateway>;
export type DeletePaymentGatewayResponse = ApiResponse<null>;

export type CreateTransactionResponse = ApiResponse<{
  transactionId: string;
  paymentUrl: string;
  expiresAt: Date;
}>;
export type GetTransactionResponse = ApiResponse<TransactionWithGateway>;

/* --------------- Tripay Specific Types --------------- */
export type TripayChannelType = z.infer<typeof tripayChannelTypeSchema>;
export type TripayTransactionStatus = z.infer<
  typeof tripayTransactionStatusSchema
>;
export type TripayFee = z.infer<typeof tripayFeeSchema>;
export type TripayChannel = z.infer<typeof tripayChannelSchema>;
export type TripayOrderItem = z.infer<typeof tripayOrderItemSchema>;
export type TripayInstruction = z.infer<typeof tripayInstructionSchema>;
export type TripayTransaction = z.infer<typeof tripayTransactionSchema>;
export type CreateTripayTransactionRequest = z.infer<
  typeof createTripayTransactionSchema
>;
export type TripayFeeCalculation = z.infer<typeof tripayFeeCalculationSchema>;
export type TripayTransactionListParams = z.infer<
  typeof tripayTransactionListParamsSchema
>;
export type TripayTransactionListResponse = z.infer<
  typeof tripayTransactionListResponseSchema
>;
