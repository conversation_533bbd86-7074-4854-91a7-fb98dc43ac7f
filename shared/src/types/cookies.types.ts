/* --------------- <PERSON>ie Types --------------- */
export interface CookieOptions {
  httpOnly?: boolean;
  secure?: boolean;
  sameSite?: "strict" | "lax" | "none";
  maxAge?: number;
  expires?: Date;
  path?: string;
  domain?: string;
}

/* --------------- <PERSON>ie Configuration Types --------------- */
export interface SecureCookieConfig {
  httpOnly: boolean;
  secure: boolean;
  sameSite: "strict" | "lax" | "none";
  path: string;
}

/* --------------- Auth Cookie Types --------------- */
export interface AuthCookieNames {
  refreshToken: string;
  sessionId: string;
  rememberMe: string;
}

export interface AuthCookieConfig {
  refreshToken: {
    name: string;
    maxAge: number; // in seconds
    httpOnly: boolean;
    secure: boolean;
    sameSite: "strict" | "lax" | "none";
  };
  sessionId: {
    name: string;
    maxAge: number; // in seconds
    httpOnly: boolean;
    secure: boolean;
    sameSite: "strict" | "lax" | "none";
  };
  rememberMe: {
    name: string;
    maxAge: number; // in seconds
    httpOnly: boolean;
    secure: boolean;
    sameSite: "strict" | "lax" | "none";
  };
}

/* --------------- Cookie Constants --------------- */
export const COOKIE_NAMES = {
  REFRESH_TOKEN: "refresh_token",
  SESSION_ID: "session_id",
  REMEMBER_ME: "remember_me",
} as const;

export const COOKIE_MAX_AGES = {
  REFRESH_TOKEN: 30 * 24 * 60 * 60, // 30 days in seconds
  SESSION_ID: 7 * 24 * 60 * 60, // 7 days in seconds
  REMEMBER_ME: 365 * 24 * 60 * 60, // 1 year in seconds
} as const;
