import * as z from "zod";
import type { ApiResponse } from "./api";
import {
  sessionStatusSchema,
  wahaSessionSchema,
  createWahaSessionSchema,
  updateWahaSessionSchema,
  wahaSessionParamsSchema,
  wahaWebhookSchema,
  wahaMessageSchema,
  wahaFileSchema,
  sendTextSchema,
  sendImageSchema,
  sendFileSchema,
  sendVoiceSchema,
  sendLocationSchema,
  sendSeenSchema,
} from "../schemas/waha.schemas";

/* --------------- Tipe Dasar & Entitas --------------- */
export type SessionStatus = z.infer<typeof sessionStatusSchema>;
export type WahaSession = z.infer<typeof wahaSessionSchema>;

/* --------------- WAHA API Message Types --------------- */
export type WahaWebhook = z.infer<typeof wahaWebhookSchema>;
export type WahaMessage = z.infer<typeof wahaMessageSchema>;
export type WahaFile = z.infer<typeof wahaFileSchema>;

/* --------------- Tipe Payload, Params & Query --------------- */
export type CreateWahaSessionPayload = z.infer<typeof createWahaSessionSchema>;
export type UpdateWahaSessionPayload = z.infer<typeof updateWahaSessionSchema>;
export type WahaSessionParams = z.infer<typeof wahaSessionParamsSchema>;

/* --------------- Send Message Request Types --------------- */
export type SendTextRequest = z.infer<typeof sendTextSchema>;
export type SendImageRequest = z.infer<typeof sendImageSchema>;
export type SendFileRequest = z.infer<typeof sendFileSchema>;
export type SendVoiceRequest = z.infer<typeof sendVoiceSchema>;
export type SendLocationRequest = z.infer<typeof sendLocationSchema>;
export type SendSeenRequest = z.infer<typeof sendSeenSchema>;

/* --------------- Tipe Respons API --------------- */
export type CreateWahaSessionResponse = ApiResponse<WahaSession>;
export type GetWahaSessionResponse = ApiResponse<WahaSession | null>;
export type UpdateWahaSessionResponse = ApiResponse<WahaSession>;
export type DeleteWahaSessionResponse = ApiResponse<null>;

// Respons untuk operasi spesifik
export type StartSessionResponse = ApiResponse<{
  status: string;
  message: string;
}>;
export type StopSessionResponse = ApiResponse<{
  status: string;
  message: string;
}>;
export type GetSessionStatusResponse = ApiResponse<{ status: SessionStatus }>;
export type GetQrCodeResponse = ApiResponse<{ qrCode: string | null }>;
