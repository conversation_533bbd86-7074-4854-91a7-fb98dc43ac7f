import * as z from "zod";
import type { ApiResponse, PaginatedResponse } from "./api";
import {
  voucherTypeSchema,
  voucherSchema,
  createVoucherSchema,
  updateVoucherSchema,
  getVoucherSchema,
  getVoucherByCodeSchema,
  getVouchersSchema,
  bulkUpdateVoucherSchema,
  bulkDeleteVoucherSchema,
  validateVoucherSchema,
  redeemVoucherSchema,
  getActiveVouchersSchema,
  voucherValidationResultSchema,
  voucherRedemptionResultSchema,
  voucherStatsSchema,
  voucherUsageHistorySchema,
} from "../schemas/voucher.schemas";

/* --------------- Tipe Dasar & Entitas --------------- */
export type VoucherType = z.infer<typeof voucherTypeSchema>;
export type Voucher = z.infer<typeof voucherSchema>;

/* --------------- Tipe Payload & Query --------------- */
export type CreateVoucherPayload = z.infer<typeof createVoucherSchema>;
export type UpdateVoucherPayload = z.infer<typeof updateVoucherSchema>;
export type GetVoucherParams = z.infer<typeof getVoucherSchema>;
export type GetVoucherByCodeParams = z.infer<typeof getVoucherByCodeSchema>;
export type GetVouchersQuery = z.infer<typeof getVouchersSchema>;
export type BulkUpdateVoucherPayload = z.infer<typeof bulkUpdateVoucherSchema>;
export type BulkDeleteVoucherPayload = z.infer<typeof bulkDeleteVoucherSchema>;
export type ValidateVoucherPayload = z.infer<typeof validateVoucherSchema>;
export type RedeemVoucherPayload = z.infer<typeof redeemVoucherSchema>;
export type GetActiveVouchersQuery = z.infer<typeof getActiveVouchersSchema>;

/* --------------- Tipe DTO (Data Transfer Objects) --------------- */
export type VoucherValidationResult = z.infer<
  typeof voucherValidationResultSchema
>;
export type VoucherRedemptionResult = z.infer<
  typeof voucherRedemptionResultSchema
>;
export type VoucherStats = z.infer<typeof voucherStatsSchema>;
export type VoucherUsageHistory = z.infer<typeof voucherUsageHistorySchema>;

/* --------------- Tipe Respons API --------------- */
export type CreateVoucherResponse = ApiResponse<Voucher>;
export type GetVoucherResponse = ApiResponse<Voucher>;
export type GetVouchersResponse = PaginatedResponse<Voucher>;
export type UpdateVoucherResponse = ApiResponse<Voucher>;
export type DeleteVoucherResponse = ApiResponse<null>;
export type BulkUpdateVoucherResponse = ApiResponse<{ count: number }>;
export type BulkDeleteVoucherResponse = ApiResponse<{ count: number }>;
export type GetActiveVouchersResponse = PaginatedResponse<Voucher>;
export type ValidateVoucherResponse = ApiResponse<VoucherValidationResult>;
export type RedeemVoucherResponse = ApiResponse<VoucherRedemptionResult>;
export type GetVoucherStatsResponse = ApiResponse<VoucherStats>;
export type GetVoucherUsageHistoryResponse =
  PaginatedResponse<VoucherUsageHistory>;
