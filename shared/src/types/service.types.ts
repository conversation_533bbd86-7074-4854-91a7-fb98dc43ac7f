import * as z from "zod";
import type { ApiResponse, PaginatedResponse } from "./api";
import {
  // Impor semua skema yang dibutuhkan
  serviceTypeSchema,
  serviceSchema,
  createServiceSchema,
  updateServiceSchema,
  serviceParamsSchema,
  getServicesSchema,
  bulkUpdateServiceSchema,
  bulkDeleteServiceSchema,
} from "../schemas/service.schemas";

/* --------------- Tipe Dasar & Entitas --------------- */
export type ServiceType = z.infer<typeof serviceTypeSchema>;
export type Service = z.infer<typeof serviceSchema>;

/* --------------- Tipe Payload, Params & Query --------------- */
export type CreateServicePayload = z.infer<typeof createServiceSchema>;
export type UpdateServicePayload = z.infer<typeof updateServiceSchema>;
export type ServiceParams = z.infer<typeof serviceParamsSchema>;
export type GetServicesQuery = z.infer<typeof getServicesSchema>;
export type BulkUpdateServicePayload = z.infer<typeof bulkUpdateServiceSchema>;
export type BulkDeleteServicePayload = z.infer<typeof bulkDeleteServiceSchema>;

/* --------------- Tipe Respons API --------------- */
export type CreateServiceResponse = ApiResponse<Service>;
export type GetServiceResponse = ApiResponse<Service>;
export type GetServicesResponse = PaginatedResponse<Service>;
export type UpdateServiceResponse = ApiResponse<Service>;
export type DeleteServiceResponse = ApiResponse<null>;
export type BulkUpdateServiceResponse = ApiResponse<{ count: number }>;
export type BulkDeleteServiceResponse = ApiResponse<{ count: number }>;
