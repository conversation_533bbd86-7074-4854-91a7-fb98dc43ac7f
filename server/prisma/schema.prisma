// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
    provider = "prisma-client-js"
    output   = "../generated/prisma"
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

model User {
    id                  String             @id @default(cuid())
    email               String             @unique
    password            String
    firstName           String?            @map("first_name")
    lastName            String?            @map("last_name")
    role                UserRole           @default(ADMIN)
    phone               String?            @map("phone")
    isActive            Boolean            @default(false) @map("is_active")
    isEmailVerified     Boolean            @default(false) @map("is_email_verified")
    createdAt           DateTime           @default(now()) @map("created_at")
    updatedAt           DateTime           @updatedAt @map("updated_at")
    security            UserSecurity?
    verifications       UserVerification[]
    sessions            UserSession[]
    blacklistedTokens   BlacklistedToken[]
    refreshTokens       RefreshToken[]
    businesses          Business[]
    businessMemberships BusinessUser[]
    invitationsSent     BusinessUser[]     @relation("UserInvitations")
    subscription        UserSubscription?
    usage               UserPackageUsage[]

    @@index([email])
    @@map("users")
}

model UserSecurity {
    id                   String              @id @default(cuid())
    userId               String              @unique @map("user_id")
    failedLoginAttempts  Int                 @default(0) @map("failed_login_attempts")
    lastFailedLoginAt    DateTime?           @map("last_failed_login_at")
    accountLockedUntil   DateTime?           @map("account_locked_until")
    lastLoginAt          DateTime?           @map("last_login_at")
    lastLoginIP          String?             @map("last_login_ip")
    lastLoginLocation    Json?               @map("last_login_location")
    lastLoginDevice      Json?               @map("last_login_device")
    twoFactorEnabled     Boolean             @default(false) @map("two_factor_enabled")
    twoFactorMethod      VerificationMethod? @map("two_factor_method")
    twoFactorSecret      String?             @map("two_factor_secret") // Encrypted TOTP secret
    twoFactorBackupCodes String[]            @map("two_factor_backup_codes")
    tempSetupToken       String?             @map("temp_setup_token")
    tempSetupSecret      String?             @map("temp_setup_secret") // Encrypted temporary secret
    tempSetupExpiresAt   DateTime?           @map("temp_setup_expires_at")
    trustedDevices       String[]            @map("trusted_devices")
    createdAt            DateTime            @default(now()) @map("created_at")
    updatedAt            DateTime            @updatedAt @map("updated_at")
    user                 User                @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@map("user_security")
}

model UserVerification {
    id            String             @id @default(cuid())
    userId        String             @map("user_id")
    type          VerificationType
    method        VerificationMethod @default(EMAIL)
    email         String?
    phoneNumber   String?            @map("phone_number")
    token         String             @unique
    code          String?
    expiresAt     DateTime           @map("expires_at")
    isVerified    Boolean            @default(false) @map("is_verified")
    verifiedAt    DateTime?          @map("verified_at")
    status        VerificationStatus @default(PENDING)
    failureReason String?            @map("failure_reason")
    attempts      Int                @default(0)
    maxAttempts   Int                @default(5) @map("max_attempts")
    metadata      Json?
    createdAt     DateTime           @default(now()) @map("created_at")
    updatedAt     DateTime           @updatedAt @map("updated_at")
    user          User               @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@index([userId, status])
    @@index([type, status])
    @@map("user_verifications")
}

model UserSession {
    id                String   @id @default(cuid())
    userId            String   @map("user_id")
    sessionToken      String   @unique @map("session_token")
    deviceInfo        Json?    @map("device_info")
    deviceFingerprint String?  @map("device_fingerprint")
    ipAddress         String   @map("ip_address")
    location          Json?
    userAgent         String?  @map("user_agent")
    isActive          Boolean  @default(true) @map("is_active")
    lastActivity      DateTime @default(now()) @map("last_activity")
    expiresAt         DateTime @map("expires_at")
    loginMethod       String?  @map("login_method") // "password", "2fa", "social"
    isTrusted         Boolean  @default(false) @map("is_trusted")
    createdAt         DateTime @default(now()) @map("created_at")
    updatedAt         DateTime @updatedAt @map("updated_at")
    user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@index([userId])
    @@index([sessionToken])
    @@index([isActive])
    @@map("user_sessions")
}

model Business {
    id                  String               @id @default(cuid())
    name                String
    description         String?
    phone               String?              @unique
    email               String?
    address             String?
    website             String?
    logo                String?
    isActive            Boolean              @default(true) @map("is_active")
    createdAt           DateTime             @default(now()) @map("created_at")
    updatedAt           DateTime             @updatedAt @map("updated_at")
    userId              String               @map("user_id")
    appointments        Appointment[]
    botProfile          BotProfile?
    businessDetail      BusinessDetail?
    businessOnboarding  BusinessOnboarding?
    user                User                 @relation(fields: [userId], references: [id], onDelete: Cascade)
    faqs                FAQ[]
    GeneratedContent    GeneratedContent[]
    orders              Order[]
    products            Product[]
    services            Service[]
    wahaSession         WahaSession?
    FollowUpTemplate    FollowUpTemplate[]
    FollowUpSchedule    FollowUpSchedule[]
    customers           Customer[]
    paymentGateways     PaymentGateway[]
    paymentTransactions PaymentTransaction[]
    businessUsers       BusinessUser[]
    FAQAnalytics        FAQAnalytics[]

    @@map("businesses")
}

model Customer {
    id                String             @id @default(cuid())
    phone             String
    name              String
    email             String?
    dateOfBirth       DateTime?          @map("date_of_birth")
    gender            String?
    address           String?
    notes             String?
    preferredLanguage String?            @default("id") @map("preferred_language")
    timezone          String?            @default("Asia/Jakarta")
    status            CustomerStatus     @default(ACTIVE)
    tags              String[]
    lifetimeValue     Decimal            @default(0) @map("lifetime_value")
    createdAt         DateTime           @default(now()) @map("created_at")
    updatedAt         DateTime           @updatedAt @map("updated_at")
    businessId        String             @map("business_id")
    business          Business           @relation(fields: [businessId], references: [id], onDelete: Cascade)
    orders            Order[]
    appointments      Appointment[]
    conversations     Conversation[]
    followUps         FollowUpSchedule[]

    @@unique([phone, businessId])
    @@index([businessId, phone])
    @@index([businessId, status])
    @@map("customers")
}

model BusinessDetail {
    id                 String       @id @default(cuid())
    businessType       BusinessType @map("business_type")
    businessHours      Json?        @map("business_hours")
    mainBenefit        String?      @map("main_benefit")
    customerProblem    String?      @map("customer_problem")
    solution           String?
    targetAudience     String?      @map("target_audience")
    promo              String?
    guarantee          String?
    uniqueSellingPoint String?      @map("unique_selling_point")
    socialProof        Json?        @map("social_proof")
    contactInfo        Json?        @map("contact_info")
    paymentMethods     Json?        @map("payment_methods")
    deliveryInfo       Json?        @map("delivery_info")
    isCompleted        Boolean      @default(false) @map("is_completed")
    createdAt          DateTime     @default(now()) @map("created_at")
    updatedAt          DateTime     @updatedAt @map("updated_at")
    businessId         String       @unique @map("business_id")
    business           Business     @relation(fields: [businessId], references: [id], onDelete: Cascade)

    @@map("business_details")
}

model BusinessOnboarding {
    id               String    @id @default(cuid())
    businessId       String    @unique @map("business_id")
    isCompleted      Boolean   @default(false) @map("is_completed")
    completedAt      DateTime? @map("completed_at")
    currentStep      Int       @default(1) @map("current_step")
    lastVisitedStep  Int       @default(1) @map("last_visited_step")
    totalSteps       Int       @default(4) @map("total_steps")
    step1Completed   Boolean   @default(false) @map("step1_completed")
    step1CompletedAt DateTime? @map("step1_completed_at")
    step1Data        Json?     @map("step1_data")
    step2Completed   Boolean   @default(false) @map("step2_completed")
    step2CompletedAt DateTime? @map("step2_completed_at")
    step2Data        Json?     @map("step2_data")
    step3Completed   Boolean   @default(false) @map("step3_completed")
    step3CompletedAt DateTime? @map("step3_completed_at")
    step3Data        Json?     @map("step3_data")
    step4Completed   Boolean   @default(false) @map("step4_completed")
    step4CompletedAt DateTime? @map("step4_completed_at")
    step4Data        Json?     @map("step4_data")
    createdAt        DateTime  @default(now()) @map("created_at")
    updatedAt        DateTime  @updatedAt @map("updated_at")
    business         Business  @relation(fields: [businessId], references: [id], onDelete: Cascade)

    @@map("business_onboarding")
}

model WahaSession {
    id            String         @id @default(cuid())
    sessionName   String         @unique @map("session_name")
    phoneNumber   String         @unique @map("phone_number")
    qrCode        String?        @map("qr_code")
    status        SessionStatus  @default(STOPPED)
    isActive      Boolean        @default(true) @map("is_active")
    createdAt     DateTime       @default(now()) @map("created_at")
    updatedAt     DateTime       @updatedAt @map("updated_at")
    businessId    String         @unique @map("business_id")
    conversations Conversation[]
    business      Business       @relation(fields: [businessId], references: [id], onDelete: Cascade)

    @@map("waha_sessions")
}

model BotProfile {
    id          String      @id @default(cuid())
    name        String
    description String?
    isActive    Boolean     @default(true) @map("is_active")
    createdAt   DateTime    @default(now()) @map("created_at")
    updatedAt   DateTime    @updatedAt @map("updated_at")
    businessId  String      @unique @map("business_id")
    business    Business    @relation(fields: [businessId], references: [id], onDelete: Cascade)
    botSetting  BotSetting?

    @@map("bot_profiles")
}

model BotSetting {
    id                    String       @id @default(uuid()) @db.Uuid
    botProfileId          String       @unique @map("bot_profile_id")
    masterAiSwitch        Boolean      @default(true) @map("master_ai_switch")
    botName               String?      @map("bot_name")
    botProfileName        String       @default("Default Friendly") @map("bot_profile_name")
    role                  String       @default("Asisten Virtual")
    tone                  String       @default("Ramah dan membantu")
    responseLength        String       @default("concise") @map("response_length")
    userSalutation        String       @default("Kak") @map("user_salutation")
    primaryLanguage       String       @default("id") @map("primary_language")
    useEmoji              Boolean      @default(true) @map("use_emoji")
    useTextStyling        Boolean      @default(true) @map("use_text_styling")
    enableRegionalDialect Boolean      @default(false) @map("enable_regional_dialect")
    dialectInstruction    String?      @map("dialect_instruction")
    vocabularyOverride    Json?        @map("vocabulary_override")
    botObjective          BotObjective
    interactionStyle      String       @default("proactive") @map("interaction_style")
    empathyLevel          Decimal      @default(0.7) @map("empathy_level") @db.Decimal(2, 1)
    maxWordsPerReply      Int          @default(150) @map("max_words_per_reply")
    fallbackMessage       String       @default("Maaf, saya kurang mengerti. Bisa diulangi dengan cara lain?") @map("fallback_message")
    pauseOnUserTyping     Boolean      @default(false) @map("pause_on_user_typing")
    typingStyle           String       @default("natural") @map("typing_style")
    typingSpeed           Int          @default(100) @map("typing_speed")
    enableSendSeen        Boolean      @default(true) @map("enable_send_seen")
    enableStartTyping     Boolean      @default(true) @map("enable_start_typing")
    enableStopTyping      Boolean      @default(true) @map("enable_stop_typing")
    createdAt             DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
    updatedAt             DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
    botProfile            BotProfile   @relation(fields: [botProfileId], references: [id], onDelete: Cascade)

    @@map("bot_settings")
}

model BotProfileTemplate {
    id          String   @id @default(cuid())
    name        String
    description String
    category    String
    botConfig   Json     @map("bot_config")
    isActive    Boolean  @default(true) @map("is_active")
    createdAt   DateTime @default(now()) @map("created_at")
    updatedAt   DateTime @updatedAt @map("updated_at")

    @@map("bot_profile_templates")
}

model Product {
    id          String      @id @default(cuid())
    name        String
    description String?
    stock       Int?
    variations  Json?
    price       Decimal
    category    String?
    image       String?
    isActive    Boolean     @default(true) @map("is_active")
    createdAt   DateTime    @default(now()) @map("created_at")
    updatedAt   DateTime    @updatedAt @map("updated_at")
    businessId  String      @map("business_id")
    orderItems  OrderItem[]
    business    Business    @relation(fields: [businessId], references: [id], onDelete: Cascade)

    @@index([businessId, isActive])
    @@index([businessId, category])
    @@map("products")
}

model Service {
    id           String        @id @default(cuid())
    name         String
    description  String?
    price        Decimal
    duration     Int?
    serviceType  ServiceType?  @default(ON_DEMAND)
    delay        Int?
    category     String?
    isActive     Boolean       @default(true) @map("is_active")
    createdAt    DateTime      @default(now()) @map("created_at")
    updatedAt    DateTime      @updatedAt @map("updated_at")
    businessId   String        @map("business_id")
    appointments Appointment[]
    business     Business      @relation(fields: [businessId], references: [id], onDelete: Cascade)

    @@index([businessId, isActive])
    @@index([businessId, category])
    @@map("services")
}

model Order {
    id                  String               @id @default(cuid())
    orderNumber         String               @unique @map("order_number")
    totalAmount         Decimal              @map("total_amount")
    status              OrderStatus          @default(PENDING)
    notes               String?
    createdAt           DateTime             @default(now()) @map("created_at")
    updatedAt           DateTime             @updatedAt @map("updated_at")
    businessId          String               @map("business_id")
    customerId          String               @map("customer_id")
    business            Business             @relation(fields: [businessId], references: [id], onDelete: Cascade)
    customer            Customer             @relation(fields: [customerId], references: [id], onDelete: Cascade)
    conversation        Conversation?
    orderItems          OrderItem[]
    FollowUpSchedule    FollowUpSchedule[]
    paymentTransactions PaymentTransaction[]

    @@index([businessId, status])
    @@index([customerId, status])
    @@index([businessId, createdAt])
    @@map("orders")
}

model OrderItem {
    id        String  @id @default(cuid())
    quantity  Int
    price     Decimal
    orderId   String  @map("order_id")
    productId String  @map("product_id")
    order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
    product   Product @relation(fields: [productId], references: [id], onDelete: Restrict) // Fixed: Protect historical data

    @@index([orderId])
    @@index([productId])
    @@map("order_items")
}

model Appointment {
    id                  String               @id @default(cuid())
    appointmentDate     DateTime             @map("appointment_date") @db.Date
    appointmentTime     String               @map("appointment_time") // Format: "HH:MM"
    endTime             String               @map("end_time") // Format: "HH:MM"
    duration            Int
    status              AppointmentStatus    @default(PENDING)
    notes               String?
    createdAt           DateTime             @default(now()) @map("created_at")
    updatedAt           DateTime             @updatedAt @map("updated_at")
    businessId          String               @map("business_id")
    serviceId           String               @map("service_id")
    customerId          String               @map("customer_id")
    business            Business             @relation(fields: [businessId], references: [id], onDelete: Cascade)
    service             Service              @relation(fields: [serviceId], references: [id], onDelete: Restrict) // Fixed: Protect historical data
    customer            Customer             @relation(fields: [customerId], references: [id], onDelete: Cascade)
    conversation        Conversation?
    FollowUpSchedule    FollowUpSchedule[]
    paymentTransactions PaymentTransaction[]

    @@index([businessId, status])
    @@index([customerId, status])
    @@index([appointmentDate])
    @@index([businessId, appointmentDate])
    @@map("appointments")
}

model FAQ {
    id               String               @id @default(cuid())
    question         String
    answer           String
    category         String?
    priority         Int                  @default(5)
    tags             String[]
    isActive         Boolean              @default(true) @map("is_active")
    createdAt        DateTime             @default(now()) @map("created_at")
    updatedAt        DateTime             @updatedAt @map("updated_at")
    businessId       String               @map("business_id")
    embedding        String?
    hasMedia         Boolean              @default(false) @map("has_media")
    mediaType        String?              @map("media_type")
    business         Business             @relation(fields: [businessId], references: [id], onDelete: Cascade)
    analytics        FAQAnalytics[]
    mediaAttachments FAQMediaAttachment[]

    @@index([businessId, isActive])
    @@index([businessId, category])
    @@index([priority])
    @@map("faqs")
}

model FAQAnalytics {
    id               String   @id @default(cuid())
    faqId            String
    query            String
    similarity       Float
    responseType     String
    confidence       Float
    searchMethod     String
    userSatisfaction String?
    processingTime   Int
    timestamp        DateTime @default(now())
    sessionId        String?
    businessId       String

    faq      FAQ      @relation(fields: [faqId], references: [id], onDelete: Cascade)
    business Business @relation(fields: [businessId], references: [id], onDelete: Cascade)

    @@index([faqId])
    @@index([businessId])
    @@index([timestamp])
    @@map("faq_analytics")
}

model MediaAttachment {
    id String @id @default(cuid())

    filename           String?
    originalName       String?
    mimetype           String
    size               Int?
    url                String
    localPath          String?
    mediaType          String
    width              Int?
    height             Int?
    duration           Int?
    isProcessed        Boolean                  @default(false)
    processedUrl       String?
    thumbnailUrl       String?
    isValidated        Boolean                  @default(false)
    isSafe             Boolean                  @default(true)
    scanResult         Json?
    createdAt          DateTime                 @default(now())
    updatedAt          DateTime                 @updatedAt
    faqAttachments     FAQMediaAttachment[]
    messageAttachments MessageMediaAttachment[]

    @@index([mediaType])
    @@index([isProcessed])
    @@index([isSafe])
    @@map("media_attachments")
}

model FAQMediaAttachment {
    id      String          @id @default(cuid())
    faqId   String
    mediaId String
    order   Int             @default(0)
    caption String?
    isMain  Boolean         @default(false)
    faq     FAQ             @relation(fields: [faqId], references: [id], onDelete: Cascade)
    media   MediaAttachment @relation(fields: [mediaId], references: [id], onDelete: Cascade)

    @@unique([faqId, mediaId])
    @@map("faq_media_attachments")
}

model MessageMediaAttachment {
    id        String          @id @default(cuid())
    messageId String
    mediaId   String
    order     Int             @default(0)
    caption   String?
    message   Message         @relation(fields: [messageId], references: [id], onDelete: Cascade)
    media     MediaAttachment @relation(fields: [mediaId], references: [id], onDelete: Cascade)

    @@unique([messageId, mediaId])
    @@map("message_media_attachments")
}

model GeneratedContent {
    id          String   @id @default(cuid())
    businessId  String
    contentType String
    targetId    String?
    metadata    Json
    createdAt   DateTime @default(now())
    business    Business @relation(fields: [businessId], references: [id], onDelete: Cascade)

    @@map("generated_content")
}

model Conversation {
    id               String             @id @default(cuid())
    isActive         Boolean            @default(true)
    context          Json?
    createdAt        DateTime           @default(now())
    updatedAt        DateTime           @updatedAt
    wahaSessionId    String
    customerId       String
    orderId          String?            @unique
    appointmentId    String?            @unique
    customer         Customer           @relation(fields: [customerId], references: [id], onDelete: Cascade)
    wahaSession      WahaSession        @relation(fields: [wahaSessionId], references: [id], onDelete: Cascade)
    appointment      Appointment?       @relation(fields: [appointmentId], references: [id], onDelete: SetNull) // Fixed: Explicit cascade behavior
    order            Order?             @relation(fields: [orderId], references: [id], onDelete: SetNull) // Fixed: Explicit cascade behavior
    messages         Message[]
    FollowUpSchedule FollowUpSchedule[]

    @@index([customerId])
    @@index([wahaSessionId])
    @@index([isActive])
    @@map("conversations")
}

model Message {
    id                     String                   @id @default(cuid())
    content                String
    isFromBot              Boolean
    messageType            String                   @default("text")
    metadata               Json?
    createdAt              DateTime                 @default(now())
    conversationId         String
    conversation           Conversation             @relation(fields: [conversationId], references: [id], onDelete: Cascade)
    MessageMediaAttachment MessageMediaAttachment[]

    @@index([conversationId])
    @@index([conversationId, createdAt])
    @@index([isFromBot])
    @@map("messages")
}

model BlacklistedToken {
    id        String   @id @default(cuid())
    token     String   @unique
    expiresAt DateTime @map("expires_at")
    createdAt DateTime @default(now()) @map("created_at")
    userId    String   @map("user_id")
    user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@map("blacklisted_tokens")
}

model RefreshToken {
    id        String   @id @default(cuid())
    token     String   @unique
    expiresAt DateTime @map("expires_at")
    createdAt DateTime @default(now()) @map("created_at")
    userId    String   @map("user_id")
    user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@index([userId])
    @@index([expiresAt])
    @@map("refresh_tokens")
}

model FollowUpTemplate {
    id              String             @id @default(cuid())
    businessId      String
    name            String // "Pre-Service Reminder", "Post-Service Check", etc
    description     String?
    followUpType    FollowUpType
    triggerEvent    TriggerEvent
    isEnabled       Boolean            @default(true)
    delayHours      Int // Hours after trigger event
    maxAttempts     Int                @default(1)
    messageTemplate String
    conditions      Json? // {"serviceCategory": "bekam", "orderAmount": ">100000"}
    createdAt       DateTime           @default(now())
    updatedAt       DateTime           @updatedAt
    business        Business           @relation(fields: [businessId], references: [id], onDelete: Cascade)
    schedules       FollowUpSchedule[]

    @@index([businessId, isEnabled])
    @@index([followUpType])
    @@index([triggerEvent])
    @@map("follow_up_templates")
}

model FollowUpSchedule {
    id              String             @id @default(cuid())
    templateId      String
    businessId      String
    customerId      String // Reference to Customer instead of storing phone/name/email
    appointmentId   String?
    orderId         String?
    conversationId  String?
    scheduledAt     DateTime
    status          FollowUpStatus     @default(PENDING)
    currentAttempt  Int                @default(0)
    finalMessage    String
    sentAt          DateTime?
    respondedAt     DateTime?
    responseContent String?
    sentiment       FollowUpSentiment?
    needsAction     Boolean            @default(false)
    actionType      String?
    metadata        Json?
    createdAt       DateTime           @default(now())
    updatedAt       DateTime           @updatedAt
    template        FollowUpTemplate   @relation(fields: [templateId], references: [id], onDelete: Cascade)
    business        Business           @relation(fields: [businessId], references: [id], onDelete: Cascade)
    customer        Customer           @relation(fields: [customerId], references: [id], onDelete: Cascade)
    appointment     Appointment?       @relation(fields: [appointmentId], references: [id], onDelete: SetNull) // Fixed: Explicit cascade behavior
    order           Order?             @relation(fields: [orderId], references: [id], onDelete: SetNull) // Fixed: Explicit cascade behavior
    conversation    Conversation?      @relation(fields: [conversationId], references: [id], onDelete: SetNull) // Fixed: Explicit cascade behavior

    @@index([businessId, status])
    @@index([customerId, scheduledAt])
    @@index([templateId])
    @@index([scheduledAt, status])
    @@map("follow_up_schedules")
}

enum BusinessType {
    PRODUCT
    SERVICE
    BOTH
}

enum SessionStatus {
    STOPPED
    STARTING
    SCAN_QR_CODE
    WORKING
    FAILED
}

enum BotObjective {
    SALES
    SUPPORT
    BOOKING
    LEAD_GENERATION
    CUSTOMER_SUPPORT
    CLOSING
    APPOINTMENT
}

enum OrderStatus {
    PENDING
    CONFIRMED
    PROCESSING
    SHIPPED
    DELIVERED
    CANCELLED
}

enum AppointmentStatus {
    PENDING
    CONFIRMED
    COMPLETED
    CANCELLED
    NO_SHOW
}

enum PaymentStatus {
    PENDING
    PAID
    FAILED
    REFUNDED
    CANCELLED
}

enum FollowUpType {
    PRE_SERVICE_REMINDER // H-4 hours before appointment
    POST_SERVICE_CHECK // H+1 day after service
    POST_SERVICE_FOLLOWUP // H+3, H+7 for progress
    DELIVERY_CONFIRMATION // Product delivery check
    PRODUCT_SATISFACTION // Post-purchase satisfaction
    REORDER_REMINDER // Based on product consumption cycle
    ABANDONED_CART // Incomplete conversations
    LEAD_NURTURING // Re-engage cold leads
    MAINTENANCE_REMINDER // Recurring service reminder
    CUSTOM // User-defined custom follow-up
}

enum TriggerEvent {
    APPOINTMENT_CREATED
    APPOINTMENT_COMPLETED
    ORDER_CREATED
    ORDER_DELIVERED
    CONVERSATION_ABANDONED
    LEAD_QUALIFIED
    CUSTOM_SCHEDULE
}

enum FollowUpStatus {
    PENDING
    SENT
    RESPONDED
    COMPLETED
    CANCELLED
    FAILED
}

enum FollowUpSentiment {
    POSITIVE
    NEGATIVE
    NEUTRAL
    NEEDS_HELP
}

enum CustomerStatus {
    ACTIVE
    INACTIVE
    BLOCKED
    VIP
}

enum PaymentProvider {
    TRIPAY
    MIDTRANS
    XENDIT
    DOKU
    FASPAY
    MANUAL
}

enum PaymentTransactionStatus {
    PENDING
    PROCESSING
    PAID
    FAILED
    EXPIRED
    CANCELLED
    REFUNDED
}

// Package System Enums
enum SubscriptionStatus {
    ACTIVE
    INACTIVE
    CANCELLED
    EXPIRED
    SUSPENDED
    TRIAL
}

enum SubscriptionPaymentStatus {
    PENDING
    PROCESSING
    PAID
    FAILED
    EXPIRED
    CANCELLED
    REFUNDED
}

// Auth System Enums
enum UserRole {
    ADMIN
    USER
    OWNER
}

enum VerificationType {
    EMAIL_VERIFICATION
    PASSWORD_RESET
    TWO_FACTOR_AUTH
    PHONE_VERIFICATION
    ACCOUNT_RECOVERY
}

enum VerificationMethod {
    EMAIL
    SMS
    WHATSAPP
}

enum VerificationStatus {
    PENDING // Just created, not sent yet
    SENT // Successfully sent
    DELIVERED // Confirmed delivered (for WhatsApp)
    FAILED // Failed to send
    EXPIRED // Token/code expired
    VERIFIED // Successfully verified
    CANCELLED // Cancelled by user/system
}

enum ServiceType {
    ON_DEMAND
    SCHEDULED
    RECURRING
}

model PaymentGateway {
    id               String               @id @default(cuid())
    businessId       String
    provider         PaymentProvider
    name             String
    isActive         Boolean              @default(true)
    isDefault        Boolean              @default(false)
    config           Json
    supportedMethods String[]
    feeConfig        Json?
    createdAt        DateTime             @default(now())
    updatedAt        DateTime             @updatedAt
    business         Business             @relation(fields: [businessId], references: [id], onDelete: Cascade)
    transactions     PaymentTransaction[]

    @@unique([businessId, name])
    @@map("payment_gateways")
}

model PaymentTransaction {
    id             String                   @id @default(cuid())
    businessId     String
    orderId        String?
    appointmentId  String?
    gatewayId      String
    amount         Decimal
    currency       String                   @default("IDR")
    paymentMethod  String
    status         PaymentTransactionStatus @default(PENDING)
    providerTxId   String?
    providerData   Json?
    paymentUrl     String?
    customerName   String
    customerPhone  String
    customerEmail  String?
    originalAmount Decimal
    feeAmount      Decimal                  @default(0)
    totalAmount    Decimal
    createdAt      DateTime                 @default(now())
    updatedAt      DateTime                 @updatedAt
    paidAt         DateTime?
    expiredAt      DateTime?
    business       Business                 @relation(fields: [businessId], references: [id], onDelete: Cascade)
    order          Order?                   @relation(fields: [orderId], references: [id], onDelete: SetNull) // Fixed: Explicit cascade behavior
    appointment    Appointment?             @relation(fields: [appointmentId], references: [id], onDelete: SetNull) // Fixed: Explicit cascade behavior
    gateway        PaymentGateway           @relation(fields: [gatewayId], references: [id], onDelete: Restrict) // Fixed: Protect gateway deletion

    @@unique([providerTxId, gatewayId])
    @@index([businessId, status])
    @@index([orderId])
    @@index([appointmentId])
    @@index([gatewayId])
    @@index([status, createdAt])
    @@map("payment_transactions")
}

model Role {
    id            String           @id @default(cuid())
    name          String // "owner", "admin", "staff", "viewer"
    displayName   String // "Business Owner", "Administrator", "Staff Member", "Viewer"
    description   String?
    isSystem      Boolean          @default(false)
    isActive      Boolean          @default(true)
    createdAt     DateTime         @default(now())
    updatedAt     DateTime         @updatedAt
    permissions   RolePermission[]
    businessUsers BusinessUser[]

    @@unique([name])
    @@map("roles")
}

model Permission {
    id          String   @id @default(cuid())
    name        String // "business.read", "product.create", "order.update", etc
    displayName String // "View Business", "Create Product", "Update Order", etc
    description String?
    module      String // "business", "product", "order", "appointment", etc
    action      String // "create", "read", "update", "delete", "manage"
    isSystem    Boolean  @default(false) // System permissions cannot be deleted
    isActive    Boolean  @default(true)
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    roles RolePermission[]

    @@unique([name])
    @@index([module])
    @@index([action])
    @@map("permissions")
}

model RolePermission {
    id           String     @id @default(cuid())
    roleId       String
    permissionId String
    createdAt    DateTime   @default(now())
    role         Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
    permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Restrict) // Fixed: Protect permission deletion

    @@unique([roleId, permissionId])
    @@index([roleId])
    @@index([permissionId])
    @@map("role_permissions")
}

model BusinessUser {
    id         String    @id @default(cuid())
    businessId String
    userId     String
    roleId     String
    isActive   Boolean   @default(true)
    invitedBy  String? // User ID who invited this user
    invitedAt  DateTime  @default(now())
    joinedAt   DateTime?
    createdAt  DateTime  @default(now())
    updatedAt  DateTime  @updatedAt
    business   Business  @relation(fields: [businessId], references: [id], onDelete: Cascade)
    user       User      @relation(fields: [userId], references: [id], onDelete: Cascade)
    role       Role      @relation(fields: [roleId], references: [id], onDelete: Restrict) // Fixed: Protect role deletion
    inviter    User?     @relation("UserInvitations", fields: [invitedBy], references: [id], onDelete: SetNull) // Fixed: Missing onDelete behavior

    @@unique([businessId, userId])
    @@index([businessId])
    @@index([userId])
    @@index([roleId])
    @@index([businessId, isActive])
    @@map("business_users")
}

model Package {
    id          String  @id @default(cuid())
    name        String  @unique
    displayName String
    description String?
    price       Decimal @default(0) // Monthly price in IDR
    currency    String  @default("IDR")

    // Package Limits for User
    limits Json
    // Example: {
    //   "max_businesses": 5,                    // Maximum businesses per user
    //   "products_per_business": 50,            // Maximum products per business
    //   "services_per_business": 20,            // Maximum services per business
    //   "appointments_per_month": 500,          // Total appointments per month for all businesses
    //   "messages_per_month": 5000,             // Total messages per month for all businesses
    //   "storage_mb": 2000,                     // Total storage for all businesses
    //   "faqs_per_business": 100,               // Maximum FAQs per business
    //   "customers_per_business": 1000,         // Maximum customers per business
    //   "custom_features": ["ai_content", "advanced_reports", "priority_support"]
    // }

    features Json? // Additional features configuration
    // Example: {
    //   "ai_content_generation": true,
    //   "advanced_analytics": false,
    //   "priority_support": false,
    //   "custom_branding": true
    // }

    discountConfig Json? // Flexible discount rules
    // Example: {
    //   "yearly_discount_percentage": 20,
    //   "renewal_discount_percentage": 10,
    //   "early_bird_discount": 15,
    //   "bulk_discount": {
    //     "min_quantity": 5,
    //     "discount_percentage": 25
    //   },
    //   "seasonal_discount": {
    //     "start_date": "2024-12-01",
    //     "end_date": "2024-12-31",
    //     "discount_percentage": 30
    //   }
    // }

    isActive          Boolean            @default(true)
    isDefault         Boolean            @default(false) // Default package for new businesses
    showInSalespage   Boolean            @default(true) // Show/hide package in salespage
    sortOrder         Int                @default(0) // For ordering packages in UI
    billingCycle      String             @default("monthly") // "monthly", "yearly", "lifetime"
    trialDays         Int                @default(0) // Free trial period in days
    createdAt         DateTime           @default(now())
    updatedAt         DateTime           @updatedAt
    userSubscriptions UserSubscription[]

    @@map("packages")
}

model UserSubscription {
    id                String                @id @default(cuid())
    userId            String                @unique // One subscription per user
    packageId         String
    status            SubscriptionStatus    @default(ACTIVE)
    startDate         DateTime              @default(now())
    endDate           DateTime? // Null for lifetime subscriptions
    nextBillingDate   DateTime? // Next payment due date
    isTrialActive     Boolean               @default(false)
    trialStartDate    DateTime?
    trialEndDate      DateTime?
    lastPaymentDate   DateTime?
    lastPaymentAmount Decimal?
    metadata          Json? // Additional subscription data
    notes             String? // Admin notes
    createdAt         DateTime              @default(now())
    updatedAt         DateTime              @updatedAt
    user              User                  @relation(fields: [userId], references: [id], onDelete: Cascade)
    package           Package               @relation(fields: [packageId], references: [id], onDelete: Restrict)
    payments          SubscriptionPayment[]

    @@index([userId])
    @@index([packageId])
    @@index([status])
    @@index([endDate])
    @@map("user_subscriptions")
}

model SubscriptionPayment {
    id                 String                    @id @default(cuid())
    subscriptionId     String
    amount             Decimal
    currency           String                    @default("IDR")
    status             SubscriptionPaymentStatus @default(PENDING)
    paymentMethod      String?
    originalAmount     Decimal
    feeAmount          Decimal                   @default(0)
    totalAmount        Decimal
    billingPeriodStart DateTime
    billingPeriodEnd   DateTime
    providerTxId       String?
    providerData       Json?
    paymentUrl         String?
    adminGatewayId     String?
    createdAt          DateTime                  @default(now())
    updatedAt          DateTime                  @updatedAt
    paidAt             DateTime?
    expiredAt          DateTime?
    subscription       UserSubscription          @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)
    adminGateway       AdminPaymentGateway?      @relation(fields: [adminGatewayId], references: [id], onDelete: SetNull)

    @@index([subscriptionId])
    @@index([status])
    @@index([adminGatewayId])
    @@index([billingPeriodStart, billingPeriodEnd])
    @@map("subscription_payments")
}

model UserPackageUsage {
    id     String @id @default(cuid())
    userId String

    periodStart DateTime
    periodEnd   DateTime

    // Current Usage Counts for all businesses under this user
    usage Json // Flexible JSON structure for usage tracking
    // Example: {
    //   "total_businesses": 3,
    //   "total_products": 25,
    //   "total_services": 8,
    //   "appointments_this_month": 45,
    //   "messages_this_month": 750,
    //   "storage_used_mb": 320,
    //   "per_business": {
    //     "business_id_1": { "products": 10, "services": 3 },
    //     "business_id_2": { "products": 15, "services": 5 }
    //   }
    // }

    lastResetDate DateTime @default(now()) // When usage was last reset
    metadata      Json? // Additional usage data

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    user User @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@unique([userId, periodStart])
    @@index([userId])
    @@index([periodStart, periodEnd])
    @@map("user_package_usage")
}

model AdminPaymentGateway {
    id                   String                @id @default(cuid())
    provider             PaymentProvider
    name                 String // "Admin Tripay", "Admin Midtrans"
    isActive             Boolean               @default(true)
    isDefault            Boolean               @default(false)
    config               Json
    supportedMethods     String[]
    feeConfig            Json?
    createdAt            DateTime              @default(now())
    updatedAt            DateTime              @updatedAt
    subscriptionPayments SubscriptionPayment[]

    @@unique([name])
    @@map("admin_payment_gateways")
}

model Voucher {
    id          String      @id @default(cuid())
    name        String      @unique
    description String?
    code        String      @unique
    type        VoucherType
    value       Decimal
    minValue    Decimal?    @map("min_value")
    maxValue    Decimal?    @map("max_value")
    startDate   DateTime    @map("start_date")
    endDate     DateTime    @map("end_date")
    isActive    Boolean     @default(true) @map("is_active")
    isRedeemed  Boolean     @default(false) @map("is_redeemed")
}

enum VoucherType {
    PERCENTAGE
    FIXED_AMOUNT
}
