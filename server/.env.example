# Server Configuration
NODE_ENV=development
PORT=3001

# Database
DATABASE_URL="postgresql://user:password@localhost:5432/takono_db"

# JWT Secrets
JWT_SECRET="your-super-secret-jwt-key-at-least-32-characters-long"
JWT_EXPIRES_IN="7d"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-at-least-32-characters-long"
JWT_REFRESH_EXPIRES_IN="30d"

# CORS
CORS_ORIGIN="*"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# WhatsApp (WAHA)
WAHA_BASE_URL="http://localhost:3000"
WAHA_API_KEY="your-waha-api-key"

# Payment Gateways
TRIPAY_API_KEY="your-tripay-api-key"
TRIPAY_PRIVATE_KEY="your-tripay-private-key"
TRIPAY_MERCHANT_CODE="your-tripay-merchant-code"
TRIPAY_MODE="sandbox"

MIDTRANS_SERVER_KEY="your-midtrans-server-key"
MIDTRANS_CLIENT_KEY="your-midtrans-client-key"
MIDTRANS_MODE="sandbox"

# Email Configuration
EMAIL_FROM="<EMAIL>"
EMAIL_FROM_NAME="Takono"
EMAIL_SERVICE="smtp"

# SMTP
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# AWS (for SES)
AWS_REGION="us-east-1"
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"

# Redis (optional)
REDIS_URL="redis://localhost:6379"

# File Storage
STORAGE_TYPE="local"
STORAGE_PATH="./uploads"

# AI Services (optional)
OPENAI_API_KEY="your-openai-api-key"
ANTHROPIC_API_KEY="your-anthropic-api-key"

# Tripay Sanbox
TRIPAY_API_KEY="DEV-f9rmiRQJ30FLDzHGxJ65QpZs6oB2HS4ssNq9m72Y"
TRIPAY_PRIVATE_KEY="AKNSj-qYg32-S7mM0-QmKzB-d5Sl8"
TRIPAY_MERCHANT_CODE="T7608"
TRIPAY_ENVIRONMENT="sandbox"