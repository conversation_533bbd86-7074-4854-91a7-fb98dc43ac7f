import { Hono } from "hono";
import { env } from "../config/env";
import { API_CONSTANTS } from "shared/dist";
import { sendSuccess } from "../utils/response";

export const healthRoutes = new Hono()
  .get("/", (c) => {
    const healthData = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: env.NODE_ENV,
      version: API_CONSTANTS.VERSION,
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
      },
      pid: process.pid,
    };

    return sendSuccess(c, healthData, "Service is healthy");
  })
  
  .get("/ready", (c) => {
    // Add readiness checks here (database, external services, etc.)
    const readyData = {
      status: "ready",
      timestamp: new Date().toISOString(),
      checks: {
        database: "ok", // TODO: Add actual database check
        // redis: "ok", // TODO: Add Redis check if used
        // external_api: "ok", // TODO: Add external API checks
      },
    };

    return sendSuccess(c, readyData, "Service is ready");
  })
  
  .get("/live", (c) => {
    // Simple liveness check
    return sendSuccess(c, { status: "alive" }, "Service is alive");
  });
