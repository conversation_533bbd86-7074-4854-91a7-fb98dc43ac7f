import { Hono } from "hono";
import { validateBody } from "../../middleware/validation";
import { sendError } from "../../utils/response";
import { hashPassword } from "../../utils/hash";
import { generateAccessToken, generateRefreshToken } from "../../utils/jwt";
import { setRefreshTokenCookie, setSessionCookie } from "../../utils/cookies";
import { generateToken } from "../../utils/crypto";
import { prisma } from "../../lib/prisma";
import { createVerificationRecord } from "./verification";
import {
  registerSchema,
  type RegisterPayload,
  type PublicUser,
  type RegisterResponse,
  SUCCESS_MESSAGES,
  ERROR_MESSAGES,
  USER_SELECT_FIELDS,
} from "shared/dist";

// Create authentication routes
const auth = new Hono()

  /**
   * POST /register
   * Register a new user account
   */
  .post("/register", validateBody(registerSchema), async (c) => {
    try {
      const data = (await c.req.json()) as RegisterPayload;

      // Check for existing user/phone outside transaction for faster error response
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { email: data.email },
            ...(data.phone ? [{ phone: data.phone }] : []),
          ],
        },
        select: { id: true, email: true, phone: true },
      });

      if (existingUser) {
        const message =
          existingUser.email === data.email
            ? "Email already registered"
            : "Phone number already registered";
        return sendError(c, message, undefined, 409);
      }

      // Hash password
      const hashedPassword = await hashPassword(data.password);

      // Get client information
      const ipAddress =
        c.req.header("x-forwarded-for") ||
        c.req.header("x-real-ip") ||
        "unknown";
      const userAgent = c.req.header("user-agent");

      // Execute all database operations in a transaction
      const result = await prisma.$transaction(async (tx) => {
        // 1. Create user
        const user = await tx.user.create({
          data: {
            email: data.email,
            password: hashedPassword,
            firstName: data.firstName,
            lastName: data.lastName,
            phone: data.phone,
            // role and isActive will use Prisma defaults (USER and true)
            isEmailVerified: false,
          },
          select: USER_SELECT_FIELDS.PUBLIC,
        });

        // 2. Generate tokens
        const accessToken = generateAccessToken({
          sub: user.id,
          email: user.email,
          role: user.role,
        });
        const refreshToken = generateRefreshToken(user.id);
        const sessionToken = generateToken(32);

        // 3. Store refresh token
        await tx.refreshToken.create({
          data: {
            token: refreshToken,
            userId: user.id,
            expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
          },
        });

        // 4. Create user session
        const sessionExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
        await tx.userSession.create({
          data: {
            userId: user.id,
            sessionToken,
            ipAddress,
            userAgent,
            expiresAt: sessionExpiresAt,
            loginMethod: "password",
            isActive: true,
            isTrusted: false,
          },
        });

        // 5. Create email verification using verification service with transaction
        const {
          verification,
          token: verificationToken,
          code: verificationCode,
        } = await createVerificationRecord(
          {
            userId: user.id,
            type: "EMAIL_VERIFICATION",
            method: "EMAIL",
            email: user.email,
            expiresInHours: 24,
          },
          tx
        ); // Pass transaction client

        // 6. Create user security record
        await tx.userSecurity.create({
          data: {
            userId: user.id,
            failedLoginAttempts: 0,
            twoFactorEnabled: false,
            trustedDevices: [],
            twoFactorBackupCodes: [],
          },
        });

        return {
          user,
          accessToken,
          refreshToken,
          sessionToken,
          verificationToken,
          verificationCode,
        };
      });

      // Set cookies after successful transaction
      setRefreshTokenCookie(c, result.refreshToken);
      setSessionCookie(c, result.sessionToken);

      // TODO: Send verification email
      // await sendVerificationEmail(result.user.email, result.verificationCode, result.verificationToken);

      const response: RegisterResponse = {
        success: true,
        message: SUCCESS_MESSAGES.REGISTRATION_SUCCESS,
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: {
          user: result.user as PublicUser,
          accessToken: result.accessToken,
          message: "Please verify your email address to complete registration",
        },
      };

      return c.json(response, 201);
    } catch (error) {
      console.error("Registration error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  });

// Export the auth routes
export { auth };
