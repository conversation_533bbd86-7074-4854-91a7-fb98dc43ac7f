import { Hono } from "hono";
import { validateBody } from "../../middleware/validation";
import { sendError } from "../../utils/response";
import { hashPassword } from "../../utils/hash";
import { generateAccessToken, generateRefreshToken } from "../../utils/jwt";
import { setRefreshTokenCookie, setSessionCookie } from "../../utils/cookies";
import { generateToken } from "../../utils/crypto";
import { prisma } from "../../lib/prisma";
import {
  registerSchema,
  type RegisterPayload,
  type PublicUser,
  type RegisterResponse,
  SUCCESS_MESSAGES,
  ERROR_MESSAGES,
  USER_SELECT_FIELDS,
} from "shared/dist";

// Create authentication routes
const auth = new Hono()

  /**
   * POST /register
   * Register a new user account
   */
  .post("/register", validateBody(registerSchema), async (c) => {
    try {
      const data = (await c.req.json()) as RegisterPayload;

      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: data.email },
        select: { id: true },
      });

      if (existingUser) {
        return sendError(c, "Email already registered", undefined, 409);
      }

      // Check if phone number already exists (if provided)
      if (data.phone) {
        const existingPhone = await prisma.user.findFirst({
          where: { phone: data.phone },
          select: { id: true },
        });

        if (existingPhone) {
          return sendError(
            c,
            "Phone number already registered",
            undefined,
            409
          );
        }
      }

      // Hash password
      const hashedPassword = await hashPassword(data.password);

      // Create user
      const user = await prisma.user.create({
        data: {
          email: data.email,
          password: hashedPassword,
          firstName: data.firstName,
          lastName: data.lastName,
          phone: data.phone,
          // role and isActive will use Prisma defaults (USER and true)
          isEmailVerified: false,
        },
        select: USER_SELECT_FIELDS.PUBLIC,
      });

      // Generate tokens
      const accessToken = generateAccessToken({
        sub: user.id,
        email: user.email,
        role: user.role,
      });

      const refreshToken = generateRefreshToken(user.id);

      // Set refresh token cookie
      setRefreshTokenCookie(c, refreshToken);

      // Store refresh token in database
      await prisma.refreshToken.create({
        data: {
          token: refreshToken,
          userId: user.id,
          expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        },
      });

      // Create user session
      const sessionToken = generateToken(32);
      const sessionExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

      // Get client information
      const ipAddress =
        c.req.header("x-forwarded-for") ||
        c.req.header("x-real-ip") ||
        "unknown";
      const userAgent = c.req.header("user-agent");

      await prisma.userSession.create({
        data: {
          userId: user.id,
          sessionToken,
          ipAddress,
          userAgent,
          expiresAt: sessionExpiresAt,
          loginMethod: "password",
          isActive: true,
          isTrusted: false,
        },
      });

      // Set session cookie
      setSessionCookie(c, sessionToken);

      // Create email verification record
      const verificationToken = generateToken(32);
      const verificationCode = Math.random().toString().slice(2, 8); // 6-digit code
      const verificationExpiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      await prisma.userVerification.create({
        data: {
          userId: user.id,
          type: "EMAIL_VERIFICATION",
          method: "EMAIL",
          email: user.email,
          token: verificationToken,
          code: verificationCode,
          expiresAt: verificationExpiresAt,
          status: "PENDING",
        },
      });

      // TODO: Send verification email
      // await sendVerificationEmail(user.email, verificationCode, verificationToken);

      // Create user security record
      await prisma.userSecurity.create({
        data: {
          userId: user.id,
          failedLoginAttempts: 0,
          twoFactorEnabled: false,
          trustedDevices: [],
          twoFactorBackupCodes: [],
        },
      });

      const response: RegisterResponse = {
        success: true,
        message: SUCCESS_MESSAGES.REGISTRATION_SUCCESS,
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: {
          user: user as PublicUser,
          accessToken,
          message: "Please verify your email address to complete registration",
        },
      };

      return c.json(response, 201);
    } catch (error) {
      console.error("Registration error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  });

// Export the auth routes
export { auth };
