import { Hono } from "hono";
import { validateQuery, validateParam } from "../../middleware/validation";
import { authenticate } from "../../middleware/auth";
import { sendSuccess, sendError } from "../../utils/response";
import { prisma } from "../../lib/prisma";
import {
  sessionParamsSchema,
  getUserSessionsSchema,
  type SessionParams,
  type GetUserSessionsQuery,
  type SessionResponse,
  type SessionsResponse,
  ERROR_MESSAGES,
} from "shared/dist";

// Create session routes
const session = new Hono()

  /**
   * GET /sessions
   * Get user sessions with pagination
   */
  .get("/", authenticate, validateQuery(getUserSessionsSchema), async (c) => {
    try {
      const user = c.get("user");
      const query = c.req.query() as unknown as GetUserSessionsQuery;

      const page = query.page || 1;
      const limit = query.limit || 10;
      const offset = (page - 1) * limit;

      // Build where clause
      const where: any = {
        userId: user.id,
      };

      if (query.isActive !== undefined) {
        where.isActive = query.isActive;
      }

      // Get sessions with pagination
      const [sessions, total] = await Promise.all([
        prisma.userSession.findMany({
          where,
          orderBy: { lastActivity: "desc" },
          skip: offset,
          take: limit,
        }),
        prisma.userSession.count({ where }),
      ]);

      const totalPages = Math.ceil(total / limit);

      const response: SessionsResponse = {
        success: true,
        data: sessions.map((session) => ({
          id: session.id,
          userId: session.userId,
          sessionToken: session.sessionToken,
          deviceInfo: session.deviceInfo as Record<string, any> | undefined,
          deviceFingerprint: session.deviceFingerprint || undefined,
          ipAddress: session.ipAddress,
          location: session.location as Record<string, any> | undefined,
          userAgent: session.userAgent || undefined,
          isActive: session.isActive,
          lastActivity: session.lastActivity,
          expiresAt: session.expiresAt,
          loginMethod: session.loginMethod || undefined,
          isTrusted: session.isTrusted,
          createdAt: session.createdAt,
          updatedAt: session.updatedAt,
        })),
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
          pagination: {
            currentPage: page,
            totalPages,
            totalItems: total,
            itemsPerPage: limit,
            hasNextPage: page < totalPages,
            hasPreviousPage: page > 1,
          },
        },
      };

      return c.json(response);
    } catch (error) {
      console.error("Get sessions error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  })

  /**
   * GET /sessions/:sessionId
   * Get specific session details
   */
  .get(
    "/:sessionId",
    authenticate,
    validateParam(sessionParamsSchema),
    async (c) => {
      try {
        const user = c.get("user");
        const { sessionId } = c.req.param() as SessionParams;

        const session = await prisma.userSession.findFirst({
          where: {
            sessionToken: sessionId,
            userId: user.id,
          },
        });

        if (!session) {
          return sendError(c, "Session not found", undefined, 404);
        }

        const response: SessionResponse = {
          success: true,
          message: "Session retrieved successfully",
          meta: {
            timestamp: new Date().toISOString(),
            version: "1.0.0",
          },
          data: {
            session: {
              id: session.id,
              userId: session.userId,
              sessionToken: session.sessionToken,
              deviceInfo: session.deviceInfo as Record<string, any> | undefined,
              deviceFingerprint: session.deviceFingerprint || undefined,
              ipAddress: session.ipAddress,
              location: session.location as Record<string, any> | undefined,
              userAgent: session.userAgent || undefined,
              isActive: session.isActive,
              lastActivity: session.lastActivity,
              expiresAt: session.expiresAt,
              loginMethod: session.loginMethod || undefined,
              isTrusted: session.isTrusted,
              createdAt: session.createdAt,
              updatedAt: session.updatedAt,
            },
          },
        };

        return c.json(response);
      } catch (error) {
        console.error("Get session error:", error);
        return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
      }
    }
  )

  /**
   * DELETE /sessions/:sessionId
   * Revoke/delete a specific session
   */
  .delete(
    "/:sessionId",
    authenticate,
    validateParam(sessionParamsSchema),
    async (c) => {
      try {
        const user = c.get("user");
        const { sessionId } = c.req.param() as SessionParams;

        const session = await prisma.userSession.findFirst({
          where: {
            sessionToken: sessionId,
            userId: user.id,
          },
        });

        if (!session) {
          return sendError(c, "Session not found", undefined, 404);
        }

        // Deactivate the session
        await prisma.userSession.update({
          where: { id: session.id },
          data: { isActive: false },
        });

        return sendSuccess(c, undefined, "Session revoked successfully");
      } catch (error) {
        console.error("Delete session error:", error);
        return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
      }
    }
  )

  /**
   * DELETE /sessions
   * Revoke all sessions except current one
   */
  .delete("/", authenticate, async (c) => {
    try {
      const user = c.get("user");
      const currentSessionId = c.get("sessionId");

      // Deactivate all sessions except current one
      await prisma.userSession.updateMany({
        where: {
          userId: user.id,
          sessionToken: {
            not: currentSessionId,
          },
          isActive: true,
        },
        data: { isActive: false },
      });

      return sendSuccess(
        c,
        undefined,
        "All other sessions revoked successfully"
      );
    } catch (error) {
      console.error("Delete all sessions error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  });

// Export the session routes
export { session };
