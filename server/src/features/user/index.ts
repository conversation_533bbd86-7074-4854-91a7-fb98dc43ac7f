import { Hono } from "hono";
import { auth } from "./authentication";
import { session } from "./session";
import { verification } from "./verification";
import { security } from "./security";

/**
 * User routes module
 * Combines all user-related routes with proper Hono chaining for RPC
 */
const userRoutes = new Hono()
  // Authentication routes - /api/user/auth/*
  .route("/auth", auth)

  // Session management routes - /api/user/sessions/*
  .route("/sessions", session)

  // Verification routes - /api/user/verification/*
  .route("/verification", verification)

  // Security routes - /api/user/security/*
  .route("/security", security);

// Export the combined user routes
export { userRoutes };
