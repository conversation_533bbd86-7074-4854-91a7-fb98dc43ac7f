import { Hono } from "hono";
import { prisma } from "../../lib/prisma";

/**
 * Security Service Functions
 * Handles user security operations like creating security records, tracking logins, etc.
 */

/**
 * Create initial security record for new user
 * Called during user registration
 */
export const createUserSecurity = async (params: {
  userId: string;
  ipAddress: string;
  userAgent?: string;
  prismaClient?: any; // Can be prisma or transaction client
}) => {
  const { userId, ipAddress, userAgent, prismaClient } = params;
  const client = prismaClient || prisma;

  // Parse user agent for device info
  const deviceInfo = {
    userAgent,
    type: "web", // Could be enhanced with device detection library
    browser: userAgent?.split(" ")[0] || "unknown",
    os: "unknown", // Could be enhanced with OS detection
    timestamp: new Date().toISOString(),
  };

  // Create security record
  const security = await client.userSecurity.create({
    data: {
      userId,
      failedLoginAttempts: 0,
      lastLoginAt: new Date(), // Set first login time
      lastLoginIP: ipAddress,
      lastLoginDevice: deviceInfo,
      twoFactorEnabled: false,
      twoFactorBackupCodes: [], // Empty string array
      trustedDevices: [],
    },
  });

  return security;
};

/**
 * Update login tracking
 * Called when user successfully logs in
 */
export const updateLoginTracking = async (params: {
  userId: string;
  ipAddress: string;
  userAgent?: string;
  location?: Record<string, any>;
  prismaClient?: any;
}) => {
  const { userId, ipAddress, userAgent, location, prismaClient } = params;
  const client = prismaClient || prisma;

  const deviceInfo = {
    userAgent,
    type: "web",
    browser: userAgent?.split(" ")[0] || "unknown",
    os: "unknown",
    timestamp: new Date().toISOString(),
  };

  // Update security record
  const security = await client.userSecurity.update({
    where: { userId },
    data: {
      lastLoginAt: new Date(),
      lastLoginIP: ipAddress,
      lastLoginDevice: deviceInfo,
      lastLoginLocation: location,
      failedLoginAttempts: 0, // Reset failed attempts on successful login
    },
  });

  return security;
};

/**
 * Track failed login attempt
 * Called when user login fails
 */
export const trackFailedLogin = async (params: {
  userId: string;
  ipAddress: string;
  maxAttempts?: number;
  lockoutDuration?: number; // in minutes
  prismaClient?: any;
}) => {
  const {
    userId,
    ipAddress,
    maxAttempts = 5,
    lockoutDuration = 15,
    prismaClient,
  } = params;
  const client = prismaClient || prisma;

  // Get current security record
  const currentSecurity = await client.userSecurity.findUnique({
    where: { userId },
    select: { failedLoginAttempts: true, accountLockedUntil: true },
  });

  if (!currentSecurity) {
    throw new Error("User security record not found");
  }

  const newFailedAttempts = currentSecurity.failedLoginAttempts + 1;
  const shouldLockAccount = newFailedAttempts >= maxAttempts;

  const updateData: any = {
    failedLoginAttempts: newFailedAttempts,
    lastFailedLoginAt: new Date(),
  };

  if (shouldLockAccount) {
    updateData.accountLockedUntil = new Date(
      Date.now() + lockoutDuration * 60 * 1000
    );
  }

  const security = await client.userSecurity.update({
    where: { userId },
    data: updateData,
  });

  return {
    security,
    isLocked: shouldLockAccount,
    attemptsRemaining: Math.max(0, maxAttempts - newFailedAttempts),
  };
};

/**
 * Check if account is locked
 */
export const isAccountLocked = async (userId: string): Promise<boolean> => {
  const security = await prisma.userSecurity.findUnique({
    where: { userId },
    select: { accountLockedUntil: true },
  });

  if (!security?.accountLockedUntil) {
    return false;
  }

  return security.accountLockedUntil > new Date();
};

// Security routes (placeholder for future implementation)
export const security = new Hono();
// TODO: Add security-related routes
// .get("/2fa/setup", ...)
// .post("/2fa/enable", ...)
// .get("/trusted-devices", ...)
// .delete("/trusted-devices/:id", ...)
