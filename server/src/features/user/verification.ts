import { Hono } from "hono";
import { validateBody } from "../../middleware/validation";
import { authenticate } from "../../middleware/auth";
import { sendError } from "../../utils/response";
import { generateToken, generateVerificationCode } from "../../utils/crypto";
import { prisma } from "../../lib/prisma";
import {
  createVerificationSchema,
  verifyTokenSchema,
  type CreateVerificationPayload,
  type VerifyTokenPayload,
  type VerificationResponse,
  type VerifyTokenResponse,
  type VerificationType,
  type VerificationMethod,
  SUCCESS_MESSAGES,
  ERROR_MESSAGES,
} from "shared/dist";
import { createExpiryDate } from "../../config/constants";

/**
 * Service function to create verification record
 * Can be called from anywhere (register, login, forgot password, etc.)
 * Supports both regular prisma client and transaction client
 */
export const createVerificationRecord = async (
  params: {
    userId: string;
    type: VerificationType;
    method?: VerificationMethod;
    email?: string;
    phoneNumber?: string;
    expiresInHours?: number;
  },
  prismaClient?: any // Can be prisma or transaction client
) => {
  const {
    userId,
    type,
    method = "EMAIL",
    email,
    phoneNumber,
    expiresInHours = 24,
  } = params;

  // Use provided client or default prisma
  const client = prismaClient || prisma;

  // Check if there's already a pending verification
  const existingVerification = await client.userVerification.findFirst({
    where: {
      userId,
      type,
      status: "PENDING",
      expiresAt: {
        gt: new Date(),
      },
    },
  });

  if (existingVerification) {
    throw new Error("A verification request is already pending");
  }

  // Generate verification token and code using secure crypto
  const token = generateToken(32);
  const code = generateVerificationCode(); // Now using secure crypto
  const expiresAt = createExpiryDate.fromHours(expiresInHours);

  // Create verification record
  const verification = await client.userVerification.create({
    data: {
      userId,
      type,
      method,
      email,
      phoneNumber,
      token,
      code,
      expiresAt,
      status: "PENDING",
    },
  });

  return {
    verification,
    token,
    code,
  };
};

// Create verification routes
const verification = new Hono()

  /**
   * POST /verification
   * Create a new verification request
   */
  .post(
    "/",
    authenticate,
    validateBody(createVerificationSchema),
    async (c) => {
      try {
        const user = c.get("user");
        const data = (await c.req.json()) as CreateVerificationPayload;

        // Use verification service to create verification record
        try {
          const { verification, code, token } = await createVerificationRecord({
            userId: user.id,
            type: data.type,
            method: data.method,
            email: data.email || user.email,
            phoneNumber: data.phoneNumber,
            expiresInHours: 0.25, // 15 minutes
          });

          // TODO: Send verification email/SMS based on method
          // await sendVerificationEmail(verification.email, code);
          // await sendVerificationSMS(verification.phoneNumber, code);

          const response: VerificationResponse = {
            success: true,
            message: SUCCESS_MESSAGES.VERIFICATION_SENT,
            meta: {
              timestamp: new Date().toISOString(),
              version: "1.0.0",
            },
            data: {
              verification: {
                id: verification.id,
                userId: verification.userId,
                type: verification.type,
                method: verification.method,
                email: verification.email ?? undefined,
                phoneNumber: verification.phoneNumber ?? undefined,
                token: verification.token,
                code: verification.code ?? undefined,
                expiresAt: verification.expiresAt,
                isVerified: verification.isVerified,
                verifiedAt: verification.verifiedAt ?? undefined,
                status: verification.status ?? undefined,
                failureReason: verification.failureReason ?? undefined,
                attempts: verification.attempts,
                maxAttempts: verification.maxAttempts,
                metadata: verification.metadata as
                  | Record<string, any>
                  | undefined,
                createdAt: verification.createdAt,
                updatedAt: verification.updatedAt,
              },
              message: "Verification code sent successfully",
            },
          };

          return c.json(response, 201);
        } catch (serviceError: any) {
          // Handle specific service errors
          if (serviceError.message.includes("already pending")) {
            return sendError(c, serviceError.message, undefined, 409);
          }
          throw serviceError; // Re-throw for outer catch
        }
      } catch (error) {
        console.error("Create verification error:", error);
        return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
      }
    }
  )

  /**
   * POST /verification/verify
   * Verify a token/code
   */
  .post("/verify", validateBody(verifyTokenSchema), async (c) => {
    try {
      const data = (await c.req.json()) as VerifyTokenPayload;

      // Find verification record
      const verification = await prisma.userVerification.findUnique({
        where: { token: data.token },
        include: { user: true },
      });

      if (!verification) {
        return sendError(c, "Invalid verification token", undefined, 400);
      }

      if (verification.status !== "PENDING") {
        return sendError(c, "Verification already processed", undefined, 400);
      }

      if (verification.expiresAt < new Date()) {
        await prisma.userVerification.update({
          where: { id: verification.id },
          data: { status: "EXPIRED" },
        });
        return sendError(c, "Verification token has expired", undefined, 400);
      }

      // Check if code is required and matches
      if (data.code && verification.code !== data.code) {
        const updatedVerification = await prisma.userVerification.update({
          where: { id: verification.id },
          data: {
            attempts: verification.attempts + 1,
            ...(verification.attempts + 1 >= verification.maxAttempts && {
              status: "EXPIRED",
              failureReason: "Maximum attempts exceeded",
            }),
          },
        });

        if (updatedVerification.attempts >= verification.maxAttempts) {
          return sendError(
            c,
            "Maximum verification attempts exceeded",
            undefined,
            400
          );
        }

        return sendError(c, "Invalid verification code", undefined, 400);
      }

      // Mark as verified
      await prisma.userVerification.update({
        where: { id: verification.id },
        data: {
          isVerified: true,
          verifiedAt: new Date(),
          status: "VERIFIED",
        },
      });

      // Update user verification status if it's email verification
      if (verification.type === "EMAIL_VERIFICATION") {
        await prisma.user.update({
          where: { id: verification.userId },
          data: { isEmailVerified: true },
        });
      }

      const response: VerifyTokenResponse = {
        success: true,
        message: SUCCESS_MESSAGES.VERIFICATION_SUCCESS,
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: {
          message: "Verification completed successfully",
          isVerified: true,
        },
      };

      return c.json(response);
    } catch (error) {
      console.error("Verify token error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  });

// Export the verification routes
export { verification };
