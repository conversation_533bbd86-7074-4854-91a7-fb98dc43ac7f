import { Hono } from "hono";
import { userRoutes } from "./user";
import { healthRoutes } from "./health";
import { apiInfoRoutes } from "./api-info";

/**
 * Features Module
 * Centralized routing for all application features
 * Maintains Hono RPC chaining compatibility
 */
const featuresApp = new Hono()
  // API info routes - / (root)
  .route("/", apiInfoRoutes)

  // Health check routes - /health/*
  .route("/health", healthRoutes)

  // User routes - /user/*
  .route("/user", userRoutes);

// Export the combined features app
export { featuresApp };

// Export individual feature routes for direct access if needed
export { apiInfoRoutes } from "./api-info";
export { userRoutes } from "./user";
export { healthRoutes } from "./health";
