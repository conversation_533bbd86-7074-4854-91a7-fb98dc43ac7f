import { Hono } from "hono";
import { userRoutes } from "./user";
import { systemRoutes } from "./system";

/**
 * Features Module
 * Centralized routing for all application features
 * Maintains Hono RPC chaining compatibility
 */
const featuresApp = new Hono()
  // System routes - / (root), /health/*, /metrics
  .route("/", systemRoutes)

  // User routes - /user/*
  .route("/user", userRoutes);

// Export the combined features app
export { featuresApp };

// Export individual feature routes for direct access if needed
export { systemRoutes } from "./system";
export { userRoutes } from "./user";
