import { Hono } from "hono";
import { sendSuccess } from "../../utils/response";
import { API_CONSTANTS } from "shared/dist";

/**
 * API Info Routes
 * Provides API information and welcome message
 */
export const apiInfoRoutes = new Hono()
  .get("/", (c) => {
    return sendSuccess(
      c,
      {
        name: "Takono API",
        version: API_CONSTANTS.VERSION,
        environment: process.env.NODE_ENV,
        timestamp: new Date().toISOString(),
        endpoints: {
          health: "/health",
          healthReady: "/health/ready", 
          healthLive: "/health/live",
          userAuth: "/user/auth",
          userSessions: "/user/sessions",
          userVerification: "/user/verification",
          docs: "/docs", // Future: API documentation
        },
        description: "Takono API - Business Management Platform",
      },
      "Welcome to Takono API"
    );
  });
