import { serve } from "bun";
import { createApp } from "./server";
import { env, isDev } from "./config/env";

/**
 * Start the server
 */
async function startServer() {
  try {
    // Validate required environment variables
    if (!env.DATABASE_URL) {
      throw new Error("DATABASE_URL is required");
    }

    if (!env.JWT_SECRET) {
      throw new Error("JWT_SECRET is required");
    }

    if (!env.JWT_REFRESH_SECRET) {
      throw new Error("JWT_REFRESH_SECRET is required");
    }

    // Create the app
    const app = createApp();

    // Start the server
    const server = serve({
      fetch: app.fetch,
      port: env.PORT,
      hostname: "0.0.0.0",
    });

    console.log(`🚀 Server started successfully!`);
    console.log(`📍 Environment: ${env.NODE_ENV}`);
    console.log(`🌐 Server running on: http://localhost:${env.PORT}`);
    console.log(`🏥 Health check: http://localhost:${env.PORT}/health`);

    if (isDev) {
      console.log(`🔧 Development mode enabled`);
      console.log(
        `📝 API docs will be available at: http://localhost:${env.PORT}/docs`
      );
    }

    // Graceful shutdown
    process.on("SIGINT", () => {
      console.log("\n🛑 Received SIGINT, shutting down gracefully...");
      server.stop();
      process.exit(0);
    });

    process.on("SIGTERM", () => {
      console.log("\n🛑 Received SIGTERM, shutting down gracefully...");
      server.stop();
      process.exit(0);
    });

    return server;
  } catch (error) {
    console.error("❌ Failed to start server:", error);
    process.exit(1);
  }
}

// Start the server if this file is run directly
if (import.meta.main) {
  startServer();
}

// Export for testing or other uses
export { createApp, startServer };
