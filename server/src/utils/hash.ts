import bcrypt from "bcryptjs";

/**
 * Hash a password using bcrypt
 */
export const hashPassword = async (password: string): Promise<string> => {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
};

/**
 * Verify a password against a hash
 */
export const verifyPassword = async (password: string, hash: string): Promise<boolean> => {
  return await bcrypt.compare(password, hash);
};

/**
 * Generate a salt for bcrypt
 */
export const generateSalt = async (rounds: number = 12): Promise<string> => {
  return await bcrypt.genSalt(rounds);
};

/**
 * Hash a string with a custom salt
 */
export const hashWithSalt = async (text: string, salt: string): Promise<string> => {
  return await bcrypt.hash(text, salt);
};
