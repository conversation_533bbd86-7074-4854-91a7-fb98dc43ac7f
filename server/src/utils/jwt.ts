import * as jwt from "jsonwebtoken";
import { env } from "../config/env";
import { jwtPayloadSchema, JWT_CONSTANTS } from "shared/dist";
import { z } from "zod";

// Type for JWT payload based on shared schema
type JWTPayload = z.infer<typeof jwtPayloadSchema>;

/**
 * Generate an access token
 */
export const generateAccessToken = (
  payload: Omit<JWTPayload, "iat" | "exp" | "jti">
): string => {
  return (jwt.sign as any)(payload, env.JWT_SECRET, {
    expiresIn: env.JWT_EXPIRES_IN,
    issuer: JWT_CONSTANTS.ISSUER,
    audience: JWT_CONSTANTS.AUDIENCE,
  });
};

/**
 * Generate a refresh token
 */
export const generateRefreshToken = (userId: string): string => {
  return (jwt.sign as any)(
    { sub: userId, type: JWT_CONSTANTS.TOKEN_TYPES.REFRESH },
    env.JWT_REFRESH_SECRET,
    {
      expiresIn: env.JWT_REFRESH_EXPIRES_IN,
      issuer: JWT_CONSTANTS.ISSUER,
      audience: JWT_CONSTANTS.AUDIENCE,
    }
  );
};

/**
 * Verify an access token
 */
export const verifyAccessToken = (token: string): JWTPayload => {
  try {
    const payload = (jwt.verify as any)(token, env.JWT_SECRET, {
      issuer: JWT_CONSTANTS.ISSUER,
      audience: JWT_CONSTANTS.AUDIENCE,
    }) as jwt.JwtPayload;

    // Validate payload structure
    return jwtPayloadSchema.parse(payload);
  } catch (error) {
    throw new Error("Invalid access token");
  }
};

/**
 * Verify a refresh token
 */
export const verifyRefreshToken = (
  token: string
): { sub: string; type: string } => {
  try {
    const payload = (jwt.verify as any)(token, env.JWT_REFRESH_SECRET, {
      issuer: JWT_CONSTANTS.ISSUER,
      audience: JWT_CONSTANTS.AUDIENCE,
    }) as jwt.JwtPayload;

    if (!payload.sub || payload.type !== JWT_CONSTANTS.TOKEN_TYPES.REFRESH) {
      throw new Error("Invalid refresh token payload");
    }

    return { sub: payload.sub, type: payload.type };
  } catch (error) {
    throw new Error("Invalid refresh token");
  }
};

/**
 * Decode a token without verification (for debugging)
 */
export const decodeToken = (token: string): jwt.JwtPayload | null => {
  try {
    return jwt.decode(token) as jwt.JwtPayload;
  } catch (error) {
    return null;
  }
};

/**
 * Extract token from Authorization header
 */
export const extractTokenFromHeader = (
  authHeader: string | undefined
): string | null => {
  if (!authHeader) return null;

  const parts = authHeader.split(" ");
  if (parts.length !== 2 || parts[0] !== "Bearer") {
    return null;
  }

  return parts[1] || null;
};

/**
 * Check if token is expired
 */
export const isTokenExpired = (token: string): boolean => {
  try {
    const decoded = decodeToken(token);
    if (!decoded || !decoded.exp) return true;

    return Date.now() >= decoded.exp * 1000;
  } catch (error) {
    return true;
  }
};

/**
 * Get token expiration time
 */
export const getTokenExpiration = (token: string): Date | null => {
  try {
    const decoded = decodeToken(token);
    if (!decoded || !decoded.exp) return null;

    return new Date(decoded.exp * 1000);
  } catch (error) {
    return null;
  }
};
