import {
  createCipheriv,
  createDecipheriv,
  randomBytes,
  randomInt,
  scrypt,
} from "crypto";
import { promisify } from "util";

const scryptAsync = promisify(scrypt);

/**
 * Encrypt text using AES-256-GCM
 */
export const encrypt = async (
  text: string,
  password: string
): Promise<string> => {
  try {
    const salt = randomBytes(16);
    const iv = randomBytes(16);

    // Derive key from password
    const key = (await scryptAsync(password, salt, 32)) as Buffer;

    const cipher = createCipheriv("aes-256-gcm", key, iv);

    let encrypted = cipher.update(text, "utf8", "hex");
    encrypted += cipher.final("hex");

    const authTag = cipher.getAuthTag();

    // Combine salt, iv, authTag, and encrypted data
    const result =
      salt.toString("hex") +
      ":" +
      iv.toString("hex") +
      ":" +
      authTag.toString("hex") +
      ":" +
      encrypted;

    return result;
  } catch (error) {
    throw new Error("Encryption failed");
  }
};

/**
 * Decrypt text using AES-256-GCM
 */
export const decrypt = async (
  encryptedData: string,
  password: string
): Promise<string> => {
  try {
    const parts = encryptedData.split(":");
    if (parts.length !== 4) {
      throw new Error("Invalid encrypted data format");
    }

    const salt = Buffer.from(parts[0]!, "hex");
    const iv = Buffer.from(parts[1]!, "hex");
    const authTag = Buffer.from(parts[2]!, "hex");
    const encrypted = parts[3]!;

    // Derive key from password
    const key = (await scryptAsync(password, salt, 32)) as Buffer;

    const decipher = createDecipheriv("aes-256-gcm", key, iv);
    decipher.setAuthTag(authTag);

    let decrypted = decipher.update(encrypted, "hex", "utf8");
    decrypted += decipher.final("utf8");

    return decrypted;
  } catch (error) {
    throw new Error("Decryption failed");
  }
};

/**
 * Generate a random token
 */
export const generateToken = (length: number = 32): string => {
  return randomBytes(length).toString("hex");
};

/**
 * Generate a random numeric code using cryptographically secure random
 */
export const generateNumericCode = (length: number = 6): string => {
  const min = Math.pow(10, length - 1);
  const max = Math.pow(10, length) - 1;
  return randomInt(min, max + 1).toString();
};

/**
 * Generate a secure 6-digit verification code
 */
export const generateVerificationCode = (): string => {
  return randomInt(100000, 999999).toString();
};

/**
 * Generate a secure random string with custom charset
 */
export const generateSecureString = (
  length: number = 16,
  charset: string = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
): string => {
  let result = "";
  const bytes = randomBytes(length);

  for (let i = 0; i < length; i++) {
    result += charset[bytes[i]! % charset.length];
  }

  return result;
};
