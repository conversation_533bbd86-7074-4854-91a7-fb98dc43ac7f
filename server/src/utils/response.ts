import type { Context } from "hono";
import type {
  ApiResponse,
  PaginatedResponse,
  PaginationMeta,
} from "shared/dist";
import { API_CONSTANTS, ERROR_MESSAGES, SUCCESS_MESSAGES } from "shared/dist";

/**
 * Create a standardized API response
 */
export const createResponse = <T = any>(
  success: boolean,
  data?: T,
  message?: string,
  errors?: string[]
): ApiResponse<T> => {
  const response: ApiResponse<T> = {
    success,
    message,
    meta: {
      timestamp: new Date().toISOString(),
      version: API_CONSTANTS.VERSION,
    },
  };

  if (data !== undefined) {
    response.data = data;
  }

  if (errors && errors.length > 0) {
    response.errors = errors;
  }

  return response;
};

/**
 * Create a success response
 */
export const successResponse = <T = any>(
  data?: T,
  message?: string
): ApiResponse<T> => {
  return createResponse(true, data, message);
};

/**
 * Create an error response
 */
export const errorResponse = (
  message: string,
  errors?: string[]
): ApiResponse => {
  return createResponse(false, undefined, message, errors);
};

/**
 * Create a paginated response
 */
export const paginatedResponse = <T = any>(
  data: T[],
  pagination: PaginationMeta,
  message?: string
): PaginatedResponse<T> => {
  return {
    success: true,
    data,
    message,
    meta: {
      timestamp: new Date().toISOString(),
      version: API_CONSTANTS.VERSION,
      pagination,
    },
  };
};

/**
 * Send a JSON response with proper status code
 */
export const sendResponse = <T = any>(
  c: Context,
  response: ApiResponse<T>,
  statusCode: number = 200
) => {
  return c.json(response, { status: statusCode as any });
};

/**
 * Send a success response
 */
export const sendSuccess = <T = any>(
  c: Context,
  data?: T,
  message?: string,
  statusCode: number = 200
) => {
  return sendResponse(c, successResponse(data, message), statusCode);
};

/**
 * Send an error response
 */
export const sendError = (
  c: Context,
  message: string,
  errors?: string[],
  statusCode: number = 400
) => {
  return sendResponse(c, errorResponse(message, errors), statusCode);
};

/**
 * Send a paginated response
 */
export const sendPaginated = <T = any>(
  c: Context,
  data: T[],
  pagination: PaginationMeta,
  message?: string,
  statusCode: number = 200
) => {
  return sendResponse(
    c,
    paginatedResponse(data, pagination, message),
    statusCode
  );
};

/**
 * Send a created response (201)
 */
export const sendCreated = <T = any>(
  c: Context,
  data?: T,
  message: string = SUCCESS_MESSAGES.CREATED
) => {
  return sendSuccess(c, data, message, 201);
};

/**
 * Send a no content response (204)
 */
export const sendNoContent = (c: Context) => {
  return c.body(null, { status: 204 });
};

/**
 * Send a not found response (404)
 */
export const sendNotFound = (
  c: Context,
  message: string = ERROR_MESSAGES.NOT_FOUND
) => {
  return sendError(c, message, undefined, 404);
};

/**
 * Send an unauthorized response (401)
 */
export const sendUnauthorized = (
  c: Context,
  message: string = ERROR_MESSAGES.UNAUTHORIZED
) => {
  return sendError(c, message, undefined, 401);
};

/**
 * Send a forbidden response (403)
 */
export const sendForbidden = (
  c: Context,
  message: string = ERROR_MESSAGES.FORBIDDEN
) => {
  return sendError(c, message, undefined, 403);
};

/**
 * Send a validation error response (422)
 */
export const sendValidationError = (
  c: Context,
  errors: string[],
  message: string = ERROR_MESSAGES.VALIDATION_FAILED
) => {
  return sendError(c, message, errors, 422);
};

/**
 * Send an internal server error response (500)
 */
export const sendInternalError = (
  c: Context,
  message: string = ERROR_MESSAGES.INTERNAL_ERROR
) => {
  return sendError(c, message, undefined, 500);
};
