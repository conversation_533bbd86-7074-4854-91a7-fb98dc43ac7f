import type { Context } from "hono";
import {
  get<PERSON><PERSON><PERSON> as getHono<PERSON>ookie,
  set<PERSON><PERSON>ie as set<PERSON>ono<PERSON>ookie,
  delete<PERSON><PERSON>ie as deleteHonoCookie,
} from "hono/cookie";
import { isProd } from "../config/env";
import type { CookieOptions } from "shared/dist";
import { COOKIE_NAMES, COOKIE_MAX_AGES } from "shared/dist";

/**
 * Default cookie options for secure cookies
 */
const defaultSecureOptions: CookieOptions = {
  httpOnly: true,
  secure: isProd, // Only secure in production
  sameSite: "lax",
  path: "/",
};

/**
 * Set a secure cookie
 */
export const setSecureCookie = (
  c: Context,
  name: string,
  value: string,
  options: CookieOptions = {}
): void => {
  const cookieOptions = { ...defaultSecureOptions, ...options };

  setHonoCookie(c, name, value, cookieOptions);
};

/**
 * Set refresh token cookie
 */
export const setRefreshTokenCookie = (c: Context, token: string): void => {
  setSecureCookie(c, COOKIE_NAMES.REFRESH_TOKEN, token, {
    maxAge: COOKIE_MAX_AGES.REFRESH_TOKEN,
    httpOnly: true,
    secure: isProd,
    sameSite: "lax",
  });
};

/**
 * Set session cookie
 */
export const setSessionCookie = (c: Context, sessionId: string): void => {
  setSecureCookie(c, COOKIE_NAMES.SESSION_ID, sessionId, {
    maxAge: COOKIE_MAX_AGES.SESSION_ID,
    httpOnly: true,
    secure: isProd,
    sameSite: "lax",
  });
};

/**
 * Get cookie value
 */
export const getCookie = (c: Context, name: string): string | undefined => {
  return getHonoCookie(c, name);
};

/**
 * Get refresh token from cookie
 */
export const getRefreshTokenFromCookie = (c: Context): string | undefined => {
  return getCookie(c, COOKIE_NAMES.REFRESH_TOKEN);
};

/**
 * Get session ID from cookie
 */
export const getSessionIdFromCookie = (c: Context): string | undefined => {
  return getCookie(c, COOKIE_NAMES.SESSION_ID);
};

/**
 * Clear a cookie
 */
export const clearCookie = (c: Context, name: string): void => {
  deleteHonoCookie(c, name, {
    path: "/",
  });
};

/**
 * Clear refresh token cookie
 */
export const clearRefreshTokenCookie = (c: Context): void => {
  clearCookie(c, COOKIE_NAMES.REFRESH_TOKEN);
};

/**
 * Clear session cookie
 */
export const clearSessionCookie = (c: Context): void => {
  clearCookie(c, COOKIE_NAMES.SESSION_ID);
};

/**
 * Clear all auth cookies
 */
export const clearAuthCookies = (c: Context): void => {
  clearRefreshTokenCookie(c);
  clearSessionCookie(c);
};

/**
 * Set remember me cookie
 */
export const setRememberMeCookie = (c: Context, value: string): void => {
  setSecureCookie(c, COOKIE_NAMES.REMEMBER_ME, value, {
    maxAge: COOKIE_MAX_AGES.REMEMBER_ME,
    httpOnly: false, // Allow client-side access for remember me functionality
    secure: isProd,
    sameSite: "lax",
  });
};

/**
 * Get remember me cookie
 */
export const getRememberMeCookie = (c: Context): string | undefined => {
  return getCookie(c, COOKIE_NAMES.REMEMBER_ME);
};

/**
 * Clear remember me cookie
 */
export const clearRememberMeCookie = (c: Context): void => {
  clearCookie(c, COOKIE_NAMES.REMEMBER_ME);
};
