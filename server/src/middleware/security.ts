import type { MiddlewareHandler } from "hono";
import { secureHeaders } from "hono/secure-headers";
import { cors } from "hono/cors";
import { env } from "../config/env";

/**
 * Security headers middleware
 */
export const securityMiddleware = (): MiddlewareHandler => {
  return secureHeaders({
    crossOriginEmbedderPolicy: false, // Disable for development
    xContentTypeOptions: "nosniff",
    xFrameOptions: "DENY",
    referrerPolicy: "strict-origin-when-cross-origin",
  });
};

/**
 * CORS middleware with configuration
 */
export const corsMiddleware = (): MiddlewareHandler => {
  return cors({
    origin: env.CORS_ORIGIN === "*" ? "*" : env.CORS_ORIGIN.split(","),
    allowMethods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
    allowHeaders: [
      "Content-Type",
      "Authorization",
      "X-Requested-With",
      "X-Request-ID",
      "X-API-Key",
    ],
    credentials: true,
    maxAge: 86400, // 24 hours
  });
};
