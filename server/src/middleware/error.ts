import type { Context } from "hono";
import { HTTPException } from "hono/http-exception";
import { ZodError } from "zod";
import {
  sendError,
  sendInternalError,
  sendValidationError,
} from "../utils/response";
import { isDev } from "../config/env";
import { ERROR_MESSAGES } from "shared/dist";

/**
 * Global error handler for Hono app
 */
export const errorHandler = (error: Error, c: Context) => {
  console.error("❌ Error:", error);

  // Handle HTTPException (thrown by Hono or custom code)
  if (error instanceof HTTPException) {
    return error.getResponse();
  }

  // Handle Zod validation errors
  if (error instanceof ZodError) {
    const errors = error.issues.map((err: any) => {
      const path = err.path.join(".");
      return `${path}: ${err.message}`;
    });

    return sendValidationError(c, errors, ERROR_MESSAGES.VALIDATION_FAILED);
  }

  // Handle Prisma errors - check by error name since Prisma types are not available
  if (error.name === "PrismaClientKnownRequestError") {
    return handlePrismaError(error as any, c);
  }

  if (error.name === "PrismaClientValidationError") {
    return sendError(c, "Database validation error", undefined, 400);
  }

  if (error.name === "PrismaClientInitializationError") {
    console.error("❌ Database connection error:", error);
    return sendInternalError(c, "Database connection failed");
  }

  // Handle JWT errors
  if (error.name === "JsonWebTokenError") {
    return sendError(c, "Invalid token", undefined, 401);
  }

  if (error.name === "TokenExpiredError") {
    return sendError(c, "Token expired", undefined, 401);
  }

  // Handle custom business logic errors
  if (error.message.includes("UNAUTHORIZED")) {
    return sendError(c, error.message, undefined, 401);
  }

  if (error.message.includes("FORBIDDEN")) {
    return sendError(c, error.message, undefined, 403);
  }

  if (error.message.includes("NOT_FOUND")) {
    return sendError(c, error.message, undefined, 404);
  }

  if (error.message.includes("CONFLICT")) {
    return sendError(c, error.message, undefined, 409);
  }

  // Default internal server error
  const message = isDev ? error.message : "Internal server error";
  return sendInternalError(c, message);
};

/**
 * Handle Prisma-specific errors
 */
const handlePrismaError = (error: any, c: Context) => {
  switch (error.code) {
    case "P2002":
      // Unique constraint violation
      const target = error.meta?.target as string[] | undefined;
      const field = target ? target[0] : "field";
      return sendError(c, `${field} already exists`, undefined, 409);

    case "P2025":
      // Record not found
      return sendError(c, ERROR_MESSAGES.NOT_FOUND, undefined, 404);

    case "P2003":
      // Foreign key constraint violation
      return sendError(c, "Related record not found", undefined, 400);

    case "P2014":
      // Required relation violation
      return sendError(c, "Required relation missing", undefined, 400);

    case "P2021":
      // Table does not exist
      return sendInternalError(c, "Database table not found");

    case "P2022":
      // Column does not exist
      return sendInternalError(c, "Database column not found");

    default:
      console.error("❌ Unhandled Prisma error:", error);
      return sendInternalError(c, "Database operation failed");
  }
};

/**
 * Custom error classes for business logic
 */
export class BusinessError extends Error {
  constructor(
    message: string,
    public statusCode: number = 400,
    public code?: string
  ) {
    super(message);
    this.name = "BusinessError";
  }
}

export class UnauthorizedError extends BusinessError {
  constructor(message: string = ERROR_MESSAGES.UNAUTHORIZED) {
    super(`UNAUTHORIZED: ${message}`, 401, "UNAUTHORIZED");
  }
}

export class ForbiddenError extends BusinessError {
  constructor(message: string = ERROR_MESSAGES.FORBIDDEN) {
    super(`FORBIDDEN: ${message}`, 403, "FORBIDDEN");
  }
}

export class NotFoundError extends BusinessError {
  constructor(message: string = ERROR_MESSAGES.NOT_FOUND) {
    super(`NOT_FOUND: ${message}`, 404, "NOT_FOUND");
  }
}

export class ConflictError extends BusinessError {
  constructor(message: string = "Resource conflict") {
    super(`CONFLICT: ${message}`, 409, "CONFLICT");
  }
}

export class ValidationError extends BusinessError {
  constructor(
    message: string = ERROR_MESSAGES.VALIDATION_FAILED,
    public errors?: string[]
  ) {
    super(message, 422, "VALIDATION_ERROR");
  }
}
