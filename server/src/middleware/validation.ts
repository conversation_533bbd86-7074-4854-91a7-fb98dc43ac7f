import { createMiddleware } from "hono/factory";
import type { ZodType } from "zod";
import { sendValidationError } from "../utils/response";

// Extend <PERSON><PERSON>'s context with validated data
declare module "hono" {
  interface ContextVariableMap {
    validatedBody: any;
    validatedQuery: any;
    validatedParam: any;
    validatedForm: any;
    validatedHeader: any;
    validatedCookie: any;
  }
}

/**
 * Validate JSON body
 */
export const validateBody = <T>(schema: ZodType<T>) => {
  return createMiddleware(async (c, next) => {
    try {
      const body = await c.req.json();
      const result = schema.safeParse(body);

      if (!result.success) {
        const errors = result.error.issues.map((err: any) => {
          const path = err.path.join(".");
          return path ? `${path}: ${err.message}` : err.message;
        });

        return sendValidationError(c, errors, "Body validation failed");
      }

      // Inject validated data to context
      c.set("validatedBody", result.data);
      await next();
    } catch (error) {
      return sendValidationError(
        c,
        ["Invalid JSON body"],
        "Body parsing failed"
      );
    }
  });
};

/**
 * Validate query parameters
 */
export const validateQuery = <T>(schema: ZodType<T>) => {
  return createMiddleware(async (c, next) => {
    const query = c.req.query();
    const result = schema.safeParse(query);

    if (!result.success) {
      const errors = result.error.issues.map((err: any) => {
        const path = err.path.join(".");
        return path ? `${path}: ${err.message}` : err.message;
      });

      return sendValidationError(c, errors, "Query validation failed");
    }

    c.set("validatedQuery", result.data);
    await next();
  });
};

/**
 * Validate path parameters
 */
export const validateParam = <T>(schema: ZodType<T>) => {
  return createMiddleware(async (c, next) => {
    const params = c.req.param();
    const result = schema.safeParse(params);

    if (!result.success) {
      const errors = result.error.issues.map((err: any) => {
        const path = err.path.join(".");
        return path ? `${path}: ${err.message}` : err.message;
      });

      return sendValidationError(c, errors, "Parameter validation failed");
    }

    c.set("validatedParam", result.data);
    await next();
  });
};

/**
 * Validate form data
 */
export const validateForm = <T>(schema: ZodType<T>) => {
  return createMiddleware(async (c, next) => {
    try {
      const formData = await c.req.formData();

      // Convert FormData to object
      const formObj: Record<string, any> = {};
      for (const [key, value] of formData.entries()) {
        formObj[key] = value;
      }

      const result = schema.safeParse(formObj);

      if (!result.success) {
        const errors = result.error.issues.map((err: any) => {
          const path = err.path.join(".");
          return path ? `${path}: ${err.message}` : err.message;
        });

        return sendValidationError(c, errors, "Form validation failed");
      }

      c.set("validatedForm", result.data);
      await next();
    } catch (error) {
      return sendValidationError(
        c,
        ["Invalid form data"],
        "Form parsing failed"
      );
    }
  });
};

/**
 * Get validated data from context
 */
export const getValidatedBody = <T>(c: any): T => {
  return c.get("validatedBody") as T;
};

export const getValidatedQuery = <T>(c: any): T => {
  return c.get("validatedQuery") as T;
};

export const getValidatedParam = <T>(c: any): T => {
  return c.get("validatedParam") as T;
};

export const getValidatedForm = <T>(c: any): T => {
  return c.get("validatedForm") as T;
};

/**
 * Example usage:
 *
 * // In your route handler:
 * app.post('/users',
 *   validateBody(createUserSchema),
 *   async (c) => {
 *     const userData = getValidatedBody<CreateUserRequest>(c);
 *     // userData is now type-safe and validated
 *     return c.json({ success: true });
 *   }
 * );
 *
 * app.get('/users/:id',
 *   validateParam(userParamsSchema),
 *   async (c) => {
 *     const { id } = getValidatedParam<{ id: string }>(c);
 *     // id is validated and type-safe
 *     return c.json({ user: await getUser(id) });
 *   }
 * );
 *
 * app.get('/users',
 *   validateQuery(getUsersSchema),
 *   async (c) => {
 *     const queryParams = getValidatedQuery<GetUsersQuery>(c);
 *     // queryParams.page, queryParams.limit, etc. are validated
 *     return c.json({ users: await getUsers(queryParams) });
 *   }
 * );
 */
