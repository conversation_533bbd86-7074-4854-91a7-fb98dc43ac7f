import type { MiddlewareHandler } from "hono";
import { logger } from "hono/logger";
import { prettyJSO<PERSON> } from "hono/pretty-json";
import { isDev } from "../config/env";

/**
 * Request logger middleware (development only)
 */
export const loggerMiddleware = (): MiddlewareHandler | undefined => {
  if (!isDev) return undefined;
  
  return logger((message, ...rest) => {
    console.log(`[${new Date().toISOString()}] ${message}`, ...rest);
  });
};

/**
 * Pretty JSON middleware (development only)
 */
export const prettyJsonMiddleware = (): MiddlewareHandler | undefined => {
  if (!isDev) return undefined;
  
  return prettyJSON({
    space: 2,
  });
};

/**
 * Custom request logger with more details
 */
export const customLoggerMiddleware = (): MiddlewareHandler => {
  return async (c, next) => {
    const start = Date.now();
    const requestId = c.get("requestId") || "unknown";
    
    // Log request
    console.log(`[${requestId}] → ${c.req.method} ${c.req.url}`);
    
    await next();
    
    // Log response
    const duration = Date.now() - start;
    const status = c.res.status;
    const statusColor = status >= 400 ? '\x1b[31m' : status >= 300 ? '\x1b[33m' : '\x1b[32m';
    
    console.log(
      `[${requestId}] ← ${statusColor}${status}\x1b[0m ${c.req.method} ${c.req.url} ${duration}ms`
    );
  };
};
