import type { MiddlewareHandler } from "hono";
import { compress } from "hono/compress";
import { timing } from "hono/timing";
import { requestId } from "hono/request-id";

/**
 * Request ID middleware for tracing
 */
export const requestIdMiddleware = (): MiddlewareHandler => {
  return requestId({
    generator: () => crypto.randomUUID(),
    headerName: "X-Request-ID",
  });
};

/**
 * Compression middleware
 */
export const compressionMiddleware = (): MiddlewareHandler => {
  return compress({
    encoding: "gzip",
  });
};

/**
 * Request timing middleware
 */
export const timingMiddleware = (): MiddlewareHandler => {
  return timing();
};

/**
 * Custom timing middleware with slow request logging
 */
export const customTimingMiddleware = (): MiddlewareHandler => {
  return async (c, next) => {
    const start = Date.now();
    await next();
    const duration = Date.now() - start;

    // Log slow requests
    if (duration > 1000) {
      console.warn(
        `Slow request: ${c.req.method} ${c.req.url} took ${duration}ms`
      );
    }

    // Add timing header
    c.res.headers.set("X-Response-Time", `${duration}ms`);
  };
};
