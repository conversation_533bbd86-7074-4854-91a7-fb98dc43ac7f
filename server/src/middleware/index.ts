import type { Hono } from "hono";
import { errorHand<PERSON> } from "./error";
import { securityMiddleware, corsMiddleware } from "./security";
import {
  requestIdMiddleware,
  timingMiddleware,
  customTimingMiddleware,
} from "./performance";
import {
  loggerMiddleware,
  prettyJsonMiddleware,
  customLoggerMiddleware,
} from "./logging";
import { isDev } from "../config/env";

/**
 * Setup all global middleware in the correct order
 */
export const setupMiddleware = (app: Hono) => {
  // 1. Request ID (must be first for tracing)
  app.use("*", requestIdMiddleware());

  // 2. Security middleware
  app.use("*", securityMiddleware());
  app.use("*", corsMiddleware());

  // 3. Performance middleware
  // app.use("*", compressionMiddleware()); // TODO: Fix CompressionStream issue
  app.use("*", timingMiddleware());
  app.use("*", customTimingMiddleware());

  // 4. Logging middleware (development only)
  if (isDev) {
    const logger = loggerMiddleware();
    const prettyJson = prettyJsonMiddleware();

    if (logger) app.use("*", logger);
    if (prettyJson) app.use("*", prettyJson);

    // Custom logger for better debugging
    app.use("*", customLoggerMiddleware());
  }

  // 5. Error handler (must be last)
  app.onError(errorHandler);

  return app;
};

// Individual middleware exports
export * from "./auth";
export * from "./error";
export * from "./validation";
export * from "./security";
export * from "./performance";
export * from "./logging";
