import { createMiddleware } from "hono/factory";
import { verifyAccessToken, extractTokenFromHeader } from "../utils/jwt";
import { getSessionIdFromCookie } from "../utils/cookies";
import { sendUnauthorized, sendForbidden } from "../utils/response";
import { prisma } from "../lib/prisma";
import type { publicUserSchema } from "shared/dist";
import { USER_SELECT_FIELDS, ERROR_MESSAGES } from "shared/dist";
import { z } from "zod";

// Type for authenticated user
type AuthUser = z.infer<typeof publicUserSchema>;

// Extend Hono's context with user data
declare module "hono" {
  interface ContextVariableMap {
    user: AuthUser;
    sessionId: string;
  }
}

/**
 * JWT Authentication middleware
 * Verifies JWT token and sets user context
 */
export const authenticate = createMiddleware(async (c, next) => {
  try {
    // Extract token from Authorization header
    const authHeader = c.req.header("Authorization");
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      return sendUnauthorized(c, ERROR_MESSAGES.TOKEN_REQUIRED);
    }

    // Verify the token
    const payload = verifyAccessToken(token);

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: payload.sub },
      select: USER_SELECT_FIELDS.PUBLIC,
    });

    if (!user) {
      return sendUnauthorized(c, ERROR_MESSAGES.USER_NOT_FOUND);
    }

    if (!user.isActive) {
      return sendUnauthorized(c, ERROR_MESSAGES.ACCOUNT_DEACTIVATED);
    }

    // Set user in context
    c.set("user", user);

    await next();
  } catch (error) {
    return sendUnauthorized(c, "Invalid or expired token");
  }
});

/**
 * Optional authentication middleware
 * Sets user context if token is valid, but doesn't require authentication
 */
export const optionalAuth = createMiddleware(async (c, next) => {
  try {
    const authHeader = c.req.header("Authorization");
    const token = extractTokenFromHeader(authHeader);

    if (token) {
      const payload = verifyAccessToken(token);

      const user = await prisma.user.findUnique({
        where: { id: payload.sub },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          phone: true,
          isActive: true,
          isEmailVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (user && user.isActive) {
        c.set("user", user);
      }
    }
  } catch (error) {
    // Ignore authentication errors in optional auth
  }

  await next();
});

/**
 * Session-based authentication middleware
 * Verifies session cookie and sets user context
 */
export const authenticateSession = createMiddleware(async (c, next) => {
  try {
    const sessionId = getSessionIdFromCookie(c);

    if (!sessionId) {
      return sendUnauthorized(c, ERROR_MESSAGES.SESSION_REQUIRED);
    }

    // Get session from database
    const session = await prisma.userSession.findUnique({
      where: { sessionToken: sessionId },
      include: {
        user: {
          select: USER_SELECT_FIELDS.PUBLIC,
        },
      },
    });

    if (!session) {
      return sendUnauthorized(c, ERROR_MESSAGES.SESSION_INVALID);
    }

    if (!session.isActive) {
      return sendUnauthorized(c, ERROR_MESSAGES.SESSION_EXPIRED);
    }

    if (session.expiresAt < new Date()) {
      return sendUnauthorized(c, ERROR_MESSAGES.SESSION_EXPIRED);
    }

    if (!session.user.isActive) {
      return sendUnauthorized(c, ERROR_MESSAGES.ACCOUNT_DEACTIVATED);
    }

    // Update last activity
    await prisma.userSession.update({
      where: { id: session.id },
      data: { lastActivity: new Date() },
    });

    // Set user and session in context
    c.set("user", session.user);
    c.set("sessionId", session.sessionToken);

    await next();
  } catch (error) {
    return sendUnauthorized(c, "Session verification failed");
  }
});

/**
 * Role-based authorization middleware
 */
export const requireRole = (...roles: string[]) => {
  return createMiddleware(async (c, next) => {
    const user = c.get("user");

    if (!user) {
      return sendUnauthorized(c, "Authentication required");
    }

    if (!roles.includes(user.role)) {
      return sendForbidden(c, "Insufficient permissions");
    }

    await next();
  });
};

/**
 * Admin role requirement
 */
export const requireAdmin = requireRole("ADMIN");

/**
 * Owner role requirement
 */
export const requireOwner = requireRole("OWNER");

/**
 * Admin or Owner role requirement
 */
export const requireAdminOrOwner = requireRole("ADMIN", "OWNER");

/**
 * Email verification requirement
 */
export const requireEmailVerified = createMiddleware(async (c, next) => {
  const user = c.get("user");

  if (!user) {
    return sendUnauthorized(c, "Authentication required");
  }

  if (!user.isEmailVerified) {
    return sendForbidden(c, "Email verification required");
  }

  await next();
});

/**
 * Business context middleware
 * Verifies user has access to the business
 */
export const requireBusinessAccess = createMiddleware(async (c, next) => {
  const user = c.get("user");
  const businessId = c.req.param("businessId");

  if (!user) {
    return sendUnauthorized(c, "Authentication required");
  }

  if (!businessId) {
    return sendForbidden(c, "Business ID required");
  }

  // Check if user has access to this business
  const businessUser = await prisma.businessUser.findFirst({
    where: {
      userId: user.id,
      businessId: businessId,
      isActive: true,
    },
    include: {
      business: true,
      role: true,
    },
  });

  if (!businessUser) {
    return sendForbidden(c, "Access to this business denied");
  }

  // Set business context
  c.set("business", businessUser.business);
  c.set("businessRole", businessUser.role);

  await next();
});
