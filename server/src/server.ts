import { Hono } from "hono";
import { setupMiddleware } from "./middleware";
import { healthRoutes } from "./routes/health";
import { userRoutes } from "./features/user";
import { sendSuccess } from "./utils/response";
import { API_CONSTANTS } from "shared/dist";

/**
 * Create and configure the Hono application
 */
export function createApp(): Hono {
  const app = new Hono();

  // Setup all middleware
  setupMiddleware(app);

  // Root route
  app.get("/", (c) => {
    return sendSuccess(
      c,
      {
        name: "Takono API",
        version: API_CONSTANTS.VERSION,
        environment: process.env.NODE_ENV,
        timestamp: new Date().toISOString(),
      },
      "Welcome to Takono API"
    );
  });

  // Health check routes
  app.route("/health", healthRoutes);

  // User routes (includes auth, sessions, verification)
  app.route("/api/user", userRoutes);

  // API routes will be added here
  // app.route("/api/payment", paymentRoutes);
  // app.route("/api/products", productRoutes);

  return app;
}
