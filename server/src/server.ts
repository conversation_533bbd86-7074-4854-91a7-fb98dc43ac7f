import { Hono } from "hono";
import { setupMiddleware } from "./middleware";
import { featuresApp } from "./features";
import { sendSuccess } from "./utils/response";
import { API_CONSTANTS } from "shared/dist";

/**
 * Create and configure the Hono application
 */
export function createApp(): Hono {
  const app = new Hono();

  // Setup all middleware
  setupMiddleware(app);

  // Mount features app - maintains RPC chaining
  app.route("/", featuresApp);

  // This gives us:
  // /health/* -> health routes
  // /user/* -> user routes (auth, sessions, verification)

  // Future: Add more features to featuresApp instead of here

  return app;
}
