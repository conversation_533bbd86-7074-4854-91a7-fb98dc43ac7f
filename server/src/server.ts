import { Hono } from "hono";
import { setupMiddleware } from "./middleware";
import { healthRoutes } from "./routes/health";
import { auth } from "./features/user/authentication";
import { verification } from "./features/user/verification";
import { session } from "./features/user/session";
import { sendSuccess } from "./utils/response";
import { API_CONSTANTS } from "shared/dist";

/**
 * Create and configure the Hono application
 */
export function createApp(): Hono {
  const app = new Hono();

  // Setup all middleware
  setupMiddleware(app);

  // Root route
  app.get("/", (c) => {
    return sendSuccess(
      c,
      {
        name: "Takono API",
        version: API_CONSTANTS.VERSION,
        environment: process.env.NODE_ENV,
        timestamp: new Date().toISOString(),
      },
      "Welcome to Takono API"
    );
  });

  // Health check routes
  app.route("/health", healthRoutes);

  // Authentication routes
  app.route("/api/auth", auth);

  // Verification routes
  app.route("/api/verification", verification);

  // Session routes
  app.route("/api/sessions", session);

  // API routes will be added here
  // app.route("/api/users", userRoutes);
  // app.route("/api/payment", paymentRoutes);

  return app;
}
