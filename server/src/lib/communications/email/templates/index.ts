/**
 * Email Templates
 * Export all email template generators
 */

// Verification email template
export { generateVerificationEmail } from "./verification";

// TODO: Add other templates
// export { generatePasswordResetEmail } from "./password-reset";
// export { generateWelcomeEmail } from "./welcome";

// Template factory
import { generateVerificationEmail } from "./verification";
import type { VerificationEmailData } from "shared/dist";

export type EmailTemplateType = "verification" | "password-reset" | "welcome";

export function generateEmailTemplate(
  type: EmailTemplateType,
  data: any
): { subject: string; html: string; text: string } {
  switch (type) {
    case "verification":
      return generateVerificationEmail(data as VerificationEmailData);
    // case "password-reset":
    //   return generatePasswordResetEmail(data as PasswordResetEmailData);
    // case "welcome":
    //   return generateWelcomeEmail(data as WelcomeEmailData);
    default:
      throw new Error(`Unsupported email template: ${type}`);
  }
}
