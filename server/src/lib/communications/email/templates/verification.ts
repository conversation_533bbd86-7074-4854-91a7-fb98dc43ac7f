import type { VerificationEmailData } from "shared/dist";

/**
 * Email Verification Template
 */
export function generateVerificationEmail(data: VerificationEmailData): {
  subject: string;
  html: string;
  text: string;
} {
  const { userName, verificationCode, verificationUrl, expiresInMinutes } = data;

  const subject = "Verify Your Email Address - Takono";

  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 2px solid #f0f0f0;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
        }
        .content {
            padding: 30px 0;
        }
        .verification-code {
            background: #f8fafc;
            border: 2px dashed #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        .code {
            font-size: 32px;
            font-weight: bold;
            color: #2563eb;
            letter-spacing: 4px;
            font-family: 'Courier New', monospace;
        }
        .button {
            display: inline-block;
            background: #2563eb;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            margin: 20px 0;
        }
        .footer {
            border-top: 1px solid #e2e8f0;
            padding-top: 20px;
            margin-top: 30px;
            font-size: 14px;
            color: #6b7280;
            text-align: center;
        }
        .warning {
            background: #fef3cd;
            border: 1px solid #fbbf24;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">Takono</div>
    </div>
    
    <div class="content">
        <h1>Verify Your Email Address</h1>
        
        <p>Hi ${userName || 'there'},</p>
        
        <p>Thank you for signing up for Takono! To complete your registration, please verify your email address using the verification code below:</p>
        
        <div class="verification-code">
            <div>Your verification code is:</div>
            <div class="code">${verificationCode}</div>
        </div>
        
        <p>Alternatively, you can click the button below to verify your email:</p>
        
        <div style="text-align: center;">
            <a href="${verificationUrl}" class="button">Verify Email Address</a>
        </div>
        
        <div class="warning">
            <strong>⚠️ Important:</strong> This verification code will expire in ${expiresInMinutes} minutes. If you didn't create an account with Takono, please ignore this email.
        </div>
        
        <p>If you have any questions, feel free to contact our support team.</p>
        
        <p>Best regards,<br>The Takono Team</p>
    </div>
    
    <div class="footer">
        <p>This email was sent to you because you signed up for a Takono account.</p>
        <p>If you didn't sign up, you can safely ignore this email.</p>
    </div>
</body>
</html>`;

  const text = `
Verify Your Email Address - Takono

Hi ${userName || 'there'},

Thank you for signing up for Takono! To complete your registration, please verify your email address using the verification code below:

Verification Code: ${verificationCode}

Alternatively, you can visit this link to verify your email:
${verificationUrl}

⚠️ Important: This verification code will expire in ${expiresInMinutes} minutes. If you didn't create an account with Takono, please ignore this email.

If you have any questions, feel free to contact our support team.

Best regards,
The Takono Team

---
This email was sent to you because you signed up for a Takono account.
If you didn't sign up, you can safely ignore this email.
`;

  return { subject, html, text };
}
