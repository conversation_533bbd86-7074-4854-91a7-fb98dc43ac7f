import { createEmailProvider, type EmailProviderType } from "./providers";
import { generateEmailTemplate, type EmailTemplateType } from "./templates";
import type {
  EmailProvider,
  EmailAddress,
  SendEmailRequest,
  EmailResponse,
  VerificationEmailData,
} from "shared/dist";

/**
 * Main Email Service
 * Provides high-level email functionality with templates and multiple providers
 */
export class EmailService {
  private provider: EmailProvider;
  private defaultFrom: EmailAddress;

  constructor(
    providerType: EmailProviderType = "ses",
    defaultFrom?: EmailAddress
  ) {
    this.provider = createEmailProvider(providerType);
    this.defaultFrom = defaultFrom || {
      email: process.env.EMAIL_FROM || "<EMAIL>",
      name: process.env.EMAIL_FROM_NAME || "Takono",
    };
  }

  /**
   * Send a basic email
   */
  async sendEmail(request: SendEmailRequest): Promise<EmailResponse> {
    return this.provider.sendEmail(request);
  }

  /**
   * Send verification email
   */
  async sendVerificationEmail(
    to: EmailAddress,
    data: VerificationEmailData
  ): Promise<EmailResponse> {
    const template = generateEmailTemplate("verification", data);

    return this.sendEmail({
      to,
      subject: template.subject,
      html: template.html,
      text: template.text,
      tags: {
        type: "verification",
        userId: data.userName,
      },
    });
  }

  /**
   * Send templated email using provider's template system
   */
  async sendTemplatedEmail(
    templateId: string,
    to: EmailAddress | EmailAddress[],
    templateData: Record<string, any>,
    options?: {
      cc?: EmailAddress | EmailAddress[];
      bcc?: EmailAddress | EmailAddress[];
      replyTo?: EmailAddress;
      tags?: Record<string, string>;
    }
  ): Promise<EmailResponse> {
    return this.provider.sendTemplatedEmail(templateId, to, templateData, options);
  }

  /**
   * Verify email address (if provider supports it)
   */
  async verifyEmailAddress(email: string): Promise<{ success: boolean }> {
    if (this.provider.verifyEmailAddress) {
      return this.provider.verifyEmailAddress(email);
    }
    throw new Error("Email verification not supported by current provider");
  }

  /**
   * Get sending statistics (if provider supports it)
   */
  async getSendingStatistics(): Promise<{
    bounces: number;
    complaints: number;
    deliveries: number;
    rejects: number;
  }> {
    if (this.provider.getSendingStatistics) {
      return this.provider.getSendingStatistics();
    }
    throw new Error("Statistics not supported by current provider");
  }

  /**
   * Get current provider name
   */
  getProviderName(): string {
    return this.provider.name;
  }
}

// Default email service instance
export const emailService = new EmailService();
