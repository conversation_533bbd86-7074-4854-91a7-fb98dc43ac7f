/**
 * Email Providers
 * Export all available email providers
 */

// AWS SES Provider
export { SESProvider } from "./ses";

// TODO: Add other providers
// export { SendGridProvider } from "./sendgrid";
// export { SMTPProvider } from "./smtp";

// Provider factory
import { SESProvider } from "./ses";
import type { EmailProvider } from "shared/dist";

export type EmailProviderType = "ses" | "sendgrid" | "smtp";

export function createEmailProvider(type: EmailProviderType): EmailProvider {
  switch (type) {
    case "ses":
      return new SESProvider();
    // case "sendgrid":
    //   return new SendGridProvider();
    // case "smtp":
    //   return new SMTPProvider();
    default:
      throw new Error(`Unsupported email provider: ${type}`);
  }
}
