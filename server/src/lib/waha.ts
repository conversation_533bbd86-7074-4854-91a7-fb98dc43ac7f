import { env } from "../config/env";
import type {
  WahaSession,
  WahaWebhook,
  WahaMessage,
  SendTextRequest,
  SendImageRequest,
  SendFileRequest,
  SendVoiceRequest,
  SendLocationRequest,
  SendSeenRequest,
} from "shared/dist";

/**
 * WAHA (WhatsApp HTTP API) Client
 */
export class WahaClient {
  private baseUrl: string;
  private apiKey?: string;

  constructor(baseUrl?: string, apiKey?: string) {
    this.baseUrl = baseUrl || env.WAHA_BASE_URL || "http://localhost:3000";
    this.apiKey = apiKey || env.WAHA_API_KEY;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      ...((options.headers as Record<string, string>) || {}),
    };

    if (this.apiKey) {
      headers["X-Api-Key"] = this.apiKey;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`WAHA API Error: ${response.status} - ${error}`);
    }

    return response.json() as Promise<T>;
  }

  /**
   * Get all sessions
   */
  async getSessions(): Promise<WahaSession[]> {
    return this.request<WahaSession[]>("/api/sessions");
  }

  /**
   * Get session by name
   */
  async getSession(sessionName: string): Promise<WahaSession> {
    return this.request<WahaSession>(`/api/sessions/${sessionName}`);
  }

  /**
   * Start a session
   */
  async startSession(sessionName: string, config?: any): Promise<WahaSession> {
    return this.request<WahaSession>(`/api/sessions/${sessionName}/start`, {
      method: "POST",
      body: JSON.stringify({ config }),
    });
  }

  /**
   * Stop a session
   */
  async stopSession(sessionName: string): Promise<void> {
    await this.request(`/api/sessions/${sessionName}/stop`, {
      method: "POST",
    });
  }

  /**
   * Get QR code for session
   */
  async getQRCode(sessionName: string): Promise<{ qr: string }> {
    return this.request<{ qr: string }>(`/api/sessions/${sessionName}/auth/qr`);
  }

  /**
   * Check if number exists on WhatsApp
   */
  async checkNumberStatus(
    sessionName: string,
    phone: string
  ): Promise<{ exists: boolean; jid?: string }> {
    return this.request<{ exists: boolean; jid?: string }>(
      `/api/checkNumberStatus`,
      {
        method: "POST",
        body: JSON.stringify({
          session: sessionName,
          phone,
        }),
      }
    );
  }

  /**
   * Send text message
   */
  async sendText(request: SendTextRequest): Promise<WahaMessage> {
    return this.request<WahaMessage>(`/api/sendText`, {
      method: "POST",
      body: JSON.stringify({
        session: request.session || "default",
        ...request,
      }),
    });
  }

  /**
   * Send image message
   */
  async sendImage(request: SendImageRequest): Promise<WahaMessage> {
    return this.request<WahaMessage>(`/api/sendImage`, {
      method: "POST",
      body: JSON.stringify({
        session: request.session || "default",
        ...request,
      }),
    });
  }

  /**
   * Send file message
   */
  async sendFile(request: SendFileRequest): Promise<WahaMessage> {
    return this.request<WahaMessage>(`/api/sendFile`, {
      method: "POST",
      body: JSON.stringify({
        session: request.session || "default",
        ...request,
      }),
    });
  }

  /**
   * Send voice message
   */
  async sendVoice(request: SendVoiceRequest): Promise<WahaMessage> {
    return this.request<WahaMessage>(`/api/sendVoice`, {
      method: "POST",
      body: JSON.stringify({
        session: request.session || "default",
        ...request,
      }),
    });
  }

  /**
   * Send location message
   */
  async sendLocation(request: SendLocationRequest): Promise<WahaMessage> {
    return this.request<WahaMessage>(`/api/sendLocation`, {
      method: "POST",
      body: JSON.stringify({
        session: request.session || "default",
        ...request,
      }),
    });
  }

  /**
   * Send seen (mark as read)
   */
  async sendSeen(request: SendSeenRequest): Promise<void> {
    await this.request(`/api/sendSeen`, {
      method: "POST",
      body: JSON.stringify({
        session: request.session || "default",
        ...request,
      }),
    });
  }

  /**
   * Get chat messages
   */
  async getMessages(
    sessionName: string,
    chatId: string,
    limit: number = 100
  ): Promise<WahaMessage[]> {
    return this.request<WahaMessage[]>(
      `/api/sessions/${sessionName}/chats/${chatId}/messages?limit=${limit}`
    );
  }

  /**
   * Get all chats
   */
  async getChats(sessionName: string): Promise<any[]> {
    return this.request<any[]>(`/api/sessions/${sessionName}/chats`);
  }

  /**
   * Set webhook
   */
  async setWebhook(sessionName: string, webhook: WahaWebhook): Promise<void> {
    await this.request(`/api/sessions/${sessionName}/webhooks`, {
      method: "POST",
      body: JSON.stringify(webhook),
    });
  }
}

// Default WAHA client instance
export const waha = new WahaClient();
