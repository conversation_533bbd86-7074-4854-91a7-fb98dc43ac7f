import { createHmac } from "crypto";
import { env } from "../config/env";
import type {
  TripayChannel,
  TripayTransaction,
  CreateTripayTransactionRequest,
  TripayFeeCalculation,
  TripayTransactionListParams,
  TripayTransactionListResponse,
} from "shared/dist";

/**
 * Tripay Payment Gateway Client
 */
export class TripayClient {
  private apiKey: string;
  private privateKey: string;
  private merchantCode: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = env.TRIPAY_API_KEY || "";
    this.privateKey = env.TRIPAY_PRIVATE_KEY || "";
    this.merchantCode = env.TRIPAY_MERCHANT_CODE || "";
    this.baseUrl =
      env.TRIPAY_MODE === "production"
        ? "https://tripay.co.id/api"
        : "https://tripay.co.id/api-sandbox";

    if (!this.apiKey || !this.privateKey || !this.merchantCode) {
      throw new Error("Tripay credentials are required");
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;

    const headers: Record<string, string> = {
      Authorization: `Bearer ${this.apiKey}`,
      "Content-Type": "application/json",
      ...((options.headers as Record<string, string>) || {}),
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    const result = (await response.json()) as any;

    if (!response.ok || !result.success) {
      throw new Error(
        `Tripay API Error: ${result.message || response.statusText}`
      );
    }

    return result.data;
  }

  /**
   * Generate signature for transaction
   */
  private generateSignature(merchantRef: string, amount: number): string {
    const payload = `${this.merchantCode}${merchantRef}${amount}`;
    return createHmac("sha256", this.privateKey).update(payload).digest("hex");
  }

  /**
   * Verify callback signature
   */
  verifyCallbackSignature(
    callbackSignature: string,
    merchantRef: string,
    amount: number,
    status: string
  ): boolean {
    const payload = `${this.merchantCode}${merchantRef}${amount}${status}`;
    const signature = createHmac("sha256", this.privateKey)
      .update(payload)
      .digest("hex");

    return signature === callbackSignature;
  }

  /**
   * Get available payment channels
   */
  async getPaymentChannels(): Promise<TripayChannel[]> {
    return this.request<TripayChannel[]>("/merchant/payment-channel");
  }

  /**
   * Get payment channel by code
   */
  async getPaymentChannel(code: string): Promise<TripayChannel> {
    const channels = await this.getPaymentChannels();
    const channel = channels.find((c) => c.code === code);

    if (!channel) {
      throw new Error(`Payment channel ${code} not found`);
    }

    return channel;
  }

  /**
   * Calculate fee for payment method
   */
  async calculateFee(
    amount: number,
    code: string
  ): Promise<TripayFeeCalculation> {
    const response = await this.request<TripayFeeCalculation>(
      `/merchant/fee-calculator`,
      {
        method: "POST",
        body: JSON.stringify({ amount, code }),
      }
    );

    return response;
  }

  /**
   * Create transaction
   */
  async createTransaction(
    request: CreateTripayTransactionRequest
  ): Promise<TripayTransaction> {
    const signature = this.generateSignature(
      request.merchant_ref,
      request.amount
    );

    const payload = {
      ...request,
      signature,
    };

    return this.request<TripayTransaction>("/transaction/create", {
      method: "POST",
      body: JSON.stringify(payload),
    });
  }

  /**
   * Get transaction detail
   */
  async getTransaction(reference: string): Promise<TripayTransaction> {
    return this.request<TripayTransaction>(
      `/transaction/detail?reference=${reference}`
    );
  }

  /**
   * Get transaction by merchant reference
   */
  async getTransactionByMerchantRef(
    merchantRef: string
  ): Promise<TripayTransaction> {
    return this.request<TripayTransaction>(
      `/transaction/detail?merchant_ref=${merchantRef}`
    );
  }

  /**
   * Get transaction list
   */
  async getTransactions(
    params?: TripayTransactionListParams
  ): Promise<TripayTransactionListResponse> {
    const queryParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const query = queryParams.toString();
    const endpoint = `/transaction/list${query ? `?${query}` : ""}`;

    return this.request<TripayTransactionListResponse>(endpoint);
  }
}

// Default Tripay client instance
export const tripay = new TripayClient();
