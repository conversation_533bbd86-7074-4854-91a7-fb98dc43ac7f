import { PrismaClient } from "../../generated/prisma";
import { isDev } from "../config/env";

// Global variable to store Prisma instance
declare global {
  var __prisma: PrismaClient | undefined;
}

// Create Prisma client with proper configuration
const createPrismaClient = () => {
  return new PrismaClient({
    log: isDev ? ["query", "error", "warn"] : ["error"],
    errorFormat: "pretty",
  });
};

// Use global variable in development to prevent multiple instances
const prisma = globalThis.__prisma ?? createPrismaClient();

if (isDev) {
  globalThis.__prisma = prisma;
}

// Graceful shutdown
process.on("beforeExit", async () => {
  await prisma.$disconnect();
});

export { prisma };
