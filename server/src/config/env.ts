export const env = {
  // Server
  NODE_ENV: process.env.NODE_ENV || "development",
  PORT: Number(process.env.PORT) || 3001,

  // Database
  DATABASE_URL: process.env.DATABASE_URL || "",

  // JWT
  JWT_SECRET: process.env.JWT_SECRET || "",
  JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || "7d",
  JWT_REFRESH_SECRET: process.env.JWT_REFRESH_SECRET || "",
  JWT_REFRESH_EXPIRES_IN: process.env.JWT_REFRESH_EXPIRES_IN || "30d",

  // CORS
  CORS_ORIGIN: process.env.CORS_ORIGIN || "*",

  // Rate Limiting
  RATE_LIMIT_WINDOW_MS:
    Number(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: Number(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,

  // WhatsApp (WAHA)
  WAHA_BASE_URL: process.env.WAHA_BASE_URL || "",
  WAHA_API_KEY: process.env.WAHA_API_KEY || "",

  // Payment Gateways
  TRIPAY_API_KEY: process.env.TRIPAY_API_KEY || "",
  TRIPAY_PRIVATE_KEY: process.env.TRIPAY_PRIVATE_KEY || "",
  TRIPAY_MERCHANT_CODE: process.env.TRIPAY_MERCHANT_CODE || "",
  TRIPAY_MODE: process.env.TRIPAY_MODE || "sandbox",

  MIDTRANS_SERVER_KEY: process.env.MIDTRANS_SERVER_KEY || "",
  MIDTRANS_CLIENT_KEY: process.env.MIDTRANS_CLIENT_KEY || "",
  MIDTRANS_MODE: process.env.MIDTRANS_MODE || "sandbox",

  // Email
  EMAIL_FROM: process.env.EMAIL_FROM || "",
  EMAIL_FROM_NAME: process.env.EMAIL_FROM_NAME || "",
  EMAIL_SERVICE: process.env.EMAIL_SERVICE || "smtp",

  // SMTP
  SMTP_HOST: process.env.SMTP_HOST || "",
  SMTP_PORT: Number(process.env.SMTP_PORT) || 587,
  SMTP_USER: process.env.SMTP_USER || "",
  SMTP_PASS: process.env.SMTP_PASS || "",

  // AWS (for SES)
  AWS_REGION: process.env.AWS_REGION || "us-east-1",
  AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID || "",
  AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY || "",

  // Redis (optional for caching/sessions)
  REDIS_URL: process.env.REDIS_URL || "",

  // File Storage
  STORAGE_TYPE: process.env.STORAGE_TYPE || "local",
  STORAGE_PATH: process.env.STORAGE_PATH || "./uploads",

  // AI Services (optional)
  OPENAI_API_KEY: process.env.OPENAI_API_KEY || "",
  ANTHROPIC_API_KEY: process.env.ANTHROPIC_API_KEY || "",
};

// Helper to check if we're in development
export const isDev = env.NODE_ENV === "development";
export const isProd = env.NODE_ENV === "production";
export const isTest = env.NODE_ENV === "test";
