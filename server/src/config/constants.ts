/**
 * Application Constants
 * Centralized constants for durations, limits, and other configuration values
 */

/* --------------- Time Durations --------------- */
export const DURATION = {
  // Milliseconds - for Date calculations
  ONE_MINUTE_MS: 60 * 1000,
  FIFTEEN_MINUTES_MS: 15 * 60 * 1000,
  ONE_HOUR_MS: 60 * 60 * 1000,
  ONE_DAY_MS: 24 * 60 * 60 * 1000,
  SEVEN_DAYS_MS: 7 * 24 * 60 * 60 * 1000,
  THIRTY_DAYS_MS: 30 * 24 * 60 * 60 * 1000,

  // Hours - for configuration
  ONE_HOUR: 1,
  TWENTY_FOUR_HOURS: 24,
  SEVEN_DAYS_HOURS: 7 * 24,

  // Minutes - for user-friendly display
  FIFTEEN_MINUTES: 15,
  TWENTY_FOUR_HOURS_MINUTES: 24 * 60,
  SEVEN_DAYS_MINUTES: 7 * 24 * 60,
} as const;

/* --------------- Security Constants --------------- */
export const SECURITY = {
  // Login attempts and lockout
  MAX_LOGIN_ATTEMPTS: 5,
  ACCOUNT_LOCKOUT_DURATION_MINUTES: 15,
  
  // Password requirements
  MIN_PASSWORD_LENGTH: 8,
  
  // Session settings
  SESSION_COOKIE_MAX_AGE: DURATION.SEVEN_DAYS_MS,
  REFRESH_TOKEN_MAX_AGE: DURATION.THIRTY_DAYS_MS,
  
  // Verification settings
  VERIFICATION_CODE_LENGTH: 6,
  EMAIL_VERIFICATION_EXPIRY_HOURS: 24,
  PASSWORD_RESET_EXPIRY_HOURS: 1,
  TWO_FACTOR_EXPIRY_MINUTES: 5,
} as const;

/* --------------- API Constants --------------- */
export const API = {
  // Rate limiting
  RATE_LIMIT_WINDOW_MINUTES: 15,
  RATE_LIMIT_MAX_REQUESTS: 100,
  
  // Pagination
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // File upload
  MAX_FILE_SIZE_MB: 10,
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'] as const,
} as const;

/* --------------- Business Constants --------------- */
export const BUSINESS = {
  // Subscription limits
  FREE_PLAN_USER_LIMIT: 5,
  FREE_PLAN_BUSINESS_LIMIT: 1,
  
  // Package usage
  DEFAULT_PACKAGE_DURATION_DAYS: 30,
  PACKAGE_GRACE_PERIOD_DAYS: 7,
} as const;

/* --------------- Environment Defaults --------------- */
export const DEFAULTS = {
  // Server
  PORT: 3001,
  NODE_ENV: 'development',
  
  // URLs
  FRONTEND_URL: 'http://localhost:3000',
  API_BASE_URL: 'http://localhost:3001',
  
  // Email
  EMAIL_FROM: '<EMAIL>',
  EMAIL_FROM_NAME: 'Takono',
} as const;

/* --------------- Helper Functions --------------- */
export const createExpiryDate = {
  /**
   * Create expiry date from now + minutes
   */
  fromMinutes: (minutes: number): Date => 
    new Date(Date.now() + minutes * DURATION.ONE_MINUTE_MS),
  
  /**
   * Create expiry date from now + hours
   */
  fromHours: (hours: number): Date => 
    new Date(Date.now() + hours * DURATION.ONE_HOUR_MS),
  
  /**
   * Create expiry date from now + days
   */
  fromDays: (days: number): Date => 
    new Date(Date.now() + days * DURATION.ONE_DAY_MS),
} as const;
