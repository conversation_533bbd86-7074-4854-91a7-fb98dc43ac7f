import { Hono } from "hono";
import { cors } from "hono/cors";
import { logger } from "hono/logger";
import { prettyJSON } from "hono/pretty-json";
import { secureHeaders } from "hono/secure-headers";
import { compress } from "hono/compress";
import { requestId } from "hono/request-id";
import { timing } from "hono/timing";
import { env, isDev } from "./env";

/**
 * Setup global middleware and configuration for the Hono app
 */
export const setupApp = (app: Hono) => {
  // Request ID for tracing
  app.use("*", requestId());

  // Security headers
  app.use("*", secureHeaders({
    crossOriginEmbedderPolicy: false, // Disable for development
  }));

  // CORS configuration
  app.use("*", cors({
    origin: env.CORS_ORIGIN === "*" ? "*" : env.CORS_ORIGIN.split(","),
    allowMethods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
    allowHeaders: [
      "Content-Type", 
      "Authorization", 
      "X-Requested-With",
      "X-Request-ID"
    ],
    credentials: true,
  }));

  // Compression
  app.use("*", compress());

  // Request timing
  app.use("*", timing());

  // Logger (only in development)
  if (isDev) {
    app.use("*", logger());
  }

  // Pretty JSON (only in development)
  if (isDev) {
    app.use("*", prettyJSON());
  }

  // Health check endpoint
  app.get("/health", (c) => {
    return c.json({
      status: "healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: env.NODE_ENV,
      version: "1.0.0",
    });
  });

  return app;
};
