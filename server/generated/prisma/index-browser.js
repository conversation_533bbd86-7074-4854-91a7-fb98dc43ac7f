
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.13.0
 * Query Engine version: 361e86d0ea4987e9f53a565309b3eed797a6bcbd
 */
Prisma.prismaVersion = {
  client: "6.13.0",
  engine: "361e86d0ea4987e9f53a565309b3eed797a6bcbd"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  password: 'password',
  firstName: 'firstName',
  lastName: 'lastName',
  role: 'role',
  phone: 'phone',
  isActive: 'isActive',
  isEmailVerified: 'isEmailVerified',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserSecurityScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  failedLoginAttempts: 'failedLoginAttempts',
  lastFailedLoginAt: 'lastFailedLoginAt',
  accountLockedUntil: 'accountLockedUntil',
  lastLoginAt: 'lastLoginAt',
  lastLoginIP: 'lastLoginIP',
  lastLoginLocation: 'lastLoginLocation',
  lastLoginDevice: 'lastLoginDevice',
  twoFactorEnabled: 'twoFactorEnabled',
  twoFactorMethod: 'twoFactorMethod',
  twoFactorSecret: 'twoFactorSecret',
  twoFactorBackupCodes: 'twoFactorBackupCodes',
  tempSetupToken: 'tempSetupToken',
  tempSetupSecret: 'tempSetupSecret',
  tempSetupExpiresAt: 'tempSetupExpiresAt',
  trustedDevices: 'trustedDevices',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserVerificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  method: 'method',
  email: 'email',
  phoneNumber: 'phoneNumber',
  token: 'token',
  code: 'code',
  expiresAt: 'expiresAt',
  isVerified: 'isVerified',
  verifiedAt: 'verifiedAt',
  status: 'status',
  failureReason: 'failureReason',
  attempts: 'attempts',
  maxAttempts: 'maxAttempts',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserSessionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  sessionToken: 'sessionToken',
  deviceInfo: 'deviceInfo',
  deviceFingerprint: 'deviceFingerprint',
  ipAddress: 'ipAddress',
  location: 'location',
  userAgent: 'userAgent',
  isActive: 'isActive',
  lastActivity: 'lastActivity',
  expiresAt: 'expiresAt',
  loginMethod: 'loginMethod',
  isTrusted: 'isTrusted',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BusinessScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  phone: 'phone',
  email: 'email',
  address: 'address',
  website: 'website',
  logo: 'logo',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
};

exports.Prisma.CustomerScalarFieldEnum = {
  id: 'id',
  phone: 'phone',
  name: 'name',
  email: 'email',
  dateOfBirth: 'dateOfBirth',
  gender: 'gender',
  address: 'address',
  notes: 'notes',
  preferredLanguage: 'preferredLanguage',
  timezone: 'timezone',
  status: 'status',
  tags: 'tags',
  lifetimeValue: 'lifetimeValue',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  businessId: 'businessId'
};

exports.Prisma.BusinessDetailScalarFieldEnum = {
  id: 'id',
  businessType: 'businessType',
  businessHours: 'businessHours',
  mainBenefit: 'mainBenefit',
  customerProblem: 'customerProblem',
  solution: 'solution',
  targetAudience: 'targetAudience',
  promo: 'promo',
  guarantee: 'guarantee',
  uniqueSellingPoint: 'uniqueSellingPoint',
  socialProof: 'socialProof',
  contactInfo: 'contactInfo',
  paymentMethods: 'paymentMethods',
  deliveryInfo: 'deliveryInfo',
  isCompleted: 'isCompleted',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  businessId: 'businessId'
};

exports.Prisma.BusinessOnboardingScalarFieldEnum = {
  id: 'id',
  businessId: 'businessId',
  isCompleted: 'isCompleted',
  completedAt: 'completedAt',
  currentStep: 'currentStep',
  lastVisitedStep: 'lastVisitedStep',
  totalSteps: 'totalSteps',
  step1Completed: 'step1Completed',
  step1CompletedAt: 'step1CompletedAt',
  step1Data: 'step1Data',
  step2Completed: 'step2Completed',
  step2CompletedAt: 'step2CompletedAt',
  step2Data: 'step2Data',
  step3Completed: 'step3Completed',
  step3CompletedAt: 'step3CompletedAt',
  step3Data: 'step3Data',
  step4Completed: 'step4Completed',
  step4CompletedAt: 'step4CompletedAt',
  step4Data: 'step4Data',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WahaSessionScalarFieldEnum = {
  id: 'id',
  sessionName: 'sessionName',
  phoneNumber: 'phoneNumber',
  qrCode: 'qrCode',
  status: 'status',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  businessId: 'businessId'
};

exports.Prisma.BotProfileScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  businessId: 'businessId'
};

exports.Prisma.BotSettingScalarFieldEnum = {
  id: 'id',
  botProfileId: 'botProfileId',
  masterAiSwitch: 'masterAiSwitch',
  botName: 'botName',
  botProfileName: 'botProfileName',
  role: 'role',
  tone: 'tone',
  responseLength: 'responseLength',
  userSalutation: 'userSalutation',
  primaryLanguage: 'primaryLanguage',
  useEmoji: 'useEmoji',
  useTextStyling: 'useTextStyling',
  enableRegionalDialect: 'enableRegionalDialect',
  dialectInstruction: 'dialectInstruction',
  vocabularyOverride: 'vocabularyOverride',
  botObjective: 'botObjective',
  interactionStyle: 'interactionStyle',
  empathyLevel: 'empathyLevel',
  maxWordsPerReply: 'maxWordsPerReply',
  fallbackMessage: 'fallbackMessage',
  pauseOnUserTyping: 'pauseOnUserTyping',
  typingStyle: 'typingStyle',
  typingSpeed: 'typingSpeed',
  enableSendSeen: 'enableSendSeen',
  enableStartTyping: 'enableStartTyping',
  enableStopTyping: 'enableStopTyping',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BotProfileTemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  category: 'category',
  botConfig: 'botConfig',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  stock: 'stock',
  variations: 'variations',
  price: 'price',
  category: 'category',
  image: 'image',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  businessId: 'businessId'
};

exports.Prisma.ServiceScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  price: 'price',
  duration: 'duration',
  serviceType: 'serviceType',
  delay: 'delay',
  category: 'category',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  businessId: 'businessId'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  orderNumber: 'orderNumber',
  totalAmount: 'totalAmount',
  status: 'status',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  businessId: 'businessId',
  customerId: 'customerId'
};

exports.Prisma.OrderItemScalarFieldEnum = {
  id: 'id',
  quantity: 'quantity',
  price: 'price',
  orderId: 'orderId',
  productId: 'productId'
};

exports.Prisma.AppointmentScalarFieldEnum = {
  id: 'id',
  appointmentDate: 'appointmentDate',
  appointmentTime: 'appointmentTime',
  endTime: 'endTime',
  duration: 'duration',
  status: 'status',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  businessId: 'businessId',
  serviceId: 'serviceId',
  customerId: 'customerId'
};

exports.Prisma.FAQScalarFieldEnum = {
  id: 'id',
  question: 'question',
  answer: 'answer',
  category: 'category',
  priority: 'priority',
  tags: 'tags',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  businessId: 'businessId',
  embedding: 'embedding',
  hasMedia: 'hasMedia',
  mediaType: 'mediaType'
};

exports.Prisma.FAQAnalyticsScalarFieldEnum = {
  id: 'id',
  faqId: 'faqId',
  query: 'query',
  similarity: 'similarity',
  responseType: 'responseType',
  confidence: 'confidence',
  searchMethod: 'searchMethod',
  userSatisfaction: 'userSatisfaction',
  processingTime: 'processingTime',
  timestamp: 'timestamp',
  sessionId: 'sessionId',
  businessId: 'businessId'
};

exports.Prisma.MediaAttachmentScalarFieldEnum = {
  id: 'id',
  filename: 'filename',
  originalName: 'originalName',
  mimetype: 'mimetype',
  size: 'size',
  url: 'url',
  localPath: 'localPath',
  mediaType: 'mediaType',
  width: 'width',
  height: 'height',
  duration: 'duration',
  isProcessed: 'isProcessed',
  processedUrl: 'processedUrl',
  thumbnailUrl: 'thumbnailUrl',
  isValidated: 'isValidated',
  isSafe: 'isSafe',
  scanResult: 'scanResult',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FAQMediaAttachmentScalarFieldEnum = {
  id: 'id',
  faqId: 'faqId',
  mediaId: 'mediaId',
  order: 'order',
  caption: 'caption',
  isMain: 'isMain'
};

exports.Prisma.MessageMediaAttachmentScalarFieldEnum = {
  id: 'id',
  messageId: 'messageId',
  mediaId: 'mediaId',
  order: 'order',
  caption: 'caption'
};

exports.Prisma.GeneratedContentScalarFieldEnum = {
  id: 'id',
  businessId: 'businessId',
  contentType: 'contentType',
  targetId: 'targetId',
  metadata: 'metadata',
  createdAt: 'createdAt'
};

exports.Prisma.ConversationScalarFieldEnum = {
  id: 'id',
  isActive: 'isActive',
  context: 'context',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  wahaSessionId: 'wahaSessionId',
  customerId: 'customerId',
  orderId: 'orderId',
  appointmentId: 'appointmentId'
};

exports.Prisma.MessageScalarFieldEnum = {
  id: 'id',
  content: 'content',
  isFromBot: 'isFromBot',
  messageType: 'messageType',
  metadata: 'metadata',
  createdAt: 'createdAt',
  conversationId: 'conversationId'
};

exports.Prisma.BlacklistedTokenScalarFieldEnum = {
  id: 'id',
  token: 'token',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  userId: 'userId'
};

exports.Prisma.RefreshTokenScalarFieldEnum = {
  id: 'id',
  token: 'token',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  userId: 'userId'
};

exports.Prisma.FollowUpTemplateScalarFieldEnum = {
  id: 'id',
  businessId: 'businessId',
  name: 'name',
  description: 'description',
  followUpType: 'followUpType',
  triggerEvent: 'triggerEvent',
  isEnabled: 'isEnabled',
  delayHours: 'delayHours',
  maxAttempts: 'maxAttempts',
  messageTemplate: 'messageTemplate',
  conditions: 'conditions',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FollowUpScheduleScalarFieldEnum = {
  id: 'id',
  templateId: 'templateId',
  businessId: 'businessId',
  customerId: 'customerId',
  appointmentId: 'appointmentId',
  orderId: 'orderId',
  conversationId: 'conversationId',
  scheduledAt: 'scheduledAt',
  status: 'status',
  currentAttempt: 'currentAttempt',
  finalMessage: 'finalMessage',
  sentAt: 'sentAt',
  respondedAt: 'respondedAt',
  responseContent: 'responseContent',
  sentiment: 'sentiment',
  needsAction: 'needsAction',
  actionType: 'actionType',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PaymentGatewayScalarFieldEnum = {
  id: 'id',
  businessId: 'businessId',
  provider: 'provider',
  name: 'name',
  isActive: 'isActive',
  isDefault: 'isDefault',
  config: 'config',
  supportedMethods: 'supportedMethods',
  feeConfig: 'feeConfig',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PaymentTransactionScalarFieldEnum = {
  id: 'id',
  businessId: 'businessId',
  orderId: 'orderId',
  appointmentId: 'appointmentId',
  gatewayId: 'gatewayId',
  amount: 'amount',
  currency: 'currency',
  paymentMethod: 'paymentMethod',
  status: 'status',
  providerTxId: 'providerTxId',
  providerData: 'providerData',
  paymentUrl: 'paymentUrl',
  customerName: 'customerName',
  customerPhone: 'customerPhone',
  customerEmail: 'customerEmail',
  originalAmount: 'originalAmount',
  feeAmount: 'feeAmount',
  totalAmount: 'totalAmount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  paidAt: 'paidAt',
  expiredAt: 'expiredAt'
};

exports.Prisma.RoleScalarFieldEnum = {
  id: 'id',
  name: 'name',
  displayName: 'displayName',
  description: 'description',
  isSystem: 'isSystem',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PermissionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  displayName: 'displayName',
  description: 'description',
  module: 'module',
  action: 'action',
  isSystem: 'isSystem',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RolePermissionScalarFieldEnum = {
  id: 'id',
  roleId: 'roleId',
  permissionId: 'permissionId',
  createdAt: 'createdAt'
};

exports.Prisma.BusinessUserScalarFieldEnum = {
  id: 'id',
  businessId: 'businessId',
  userId: 'userId',
  roleId: 'roleId',
  isActive: 'isActive',
  invitedBy: 'invitedBy',
  invitedAt: 'invitedAt',
  joinedAt: 'joinedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PackageScalarFieldEnum = {
  id: 'id',
  name: 'name',
  displayName: 'displayName',
  description: 'description',
  price: 'price',
  currency: 'currency',
  limits: 'limits',
  features: 'features',
  discountConfig: 'discountConfig',
  isActive: 'isActive',
  isDefault: 'isDefault',
  showInSalespage: 'showInSalespage',
  sortOrder: 'sortOrder',
  billingCycle: 'billingCycle',
  trialDays: 'trialDays',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserSubscriptionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  packageId: 'packageId',
  status: 'status',
  startDate: 'startDate',
  endDate: 'endDate',
  nextBillingDate: 'nextBillingDate',
  isTrialActive: 'isTrialActive',
  trialStartDate: 'trialStartDate',
  trialEndDate: 'trialEndDate',
  lastPaymentDate: 'lastPaymentDate',
  lastPaymentAmount: 'lastPaymentAmount',
  metadata: 'metadata',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SubscriptionPaymentScalarFieldEnum = {
  id: 'id',
  subscriptionId: 'subscriptionId',
  amount: 'amount',
  currency: 'currency',
  status: 'status',
  paymentMethod: 'paymentMethod',
  originalAmount: 'originalAmount',
  feeAmount: 'feeAmount',
  totalAmount: 'totalAmount',
  billingPeriodStart: 'billingPeriodStart',
  billingPeriodEnd: 'billingPeriodEnd',
  providerTxId: 'providerTxId',
  providerData: 'providerData',
  paymentUrl: 'paymentUrl',
  adminGatewayId: 'adminGatewayId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  paidAt: 'paidAt',
  expiredAt: 'expiredAt'
};

exports.Prisma.UserPackageUsageScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  periodStart: 'periodStart',
  periodEnd: 'periodEnd',
  usage: 'usage',
  lastResetDate: 'lastResetDate',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AdminPaymentGatewayScalarFieldEnum = {
  id: 'id',
  provider: 'provider',
  name: 'name',
  isActive: 'isActive',
  isDefault: 'isDefault',
  config: 'config',
  supportedMethods: 'supportedMethods',
  feeConfig: 'feeConfig',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.VoucherScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  code: 'code',
  type: 'type',
  value: 'value',
  minValue: 'minValue',
  maxValue: 'maxValue',
  startDate: 'startDate',
  endDate: 'endDate',
  isActive: 'isActive',
  isRedeemed: 'isRedeemed'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserRole = exports.$Enums.UserRole = {
  ADMIN: 'ADMIN',
  USER: 'USER',
  OWNER: 'OWNER'
};

exports.VerificationMethod = exports.$Enums.VerificationMethod = {
  EMAIL: 'EMAIL',
  SMS: 'SMS',
  WHATSAPP: 'WHATSAPP'
};

exports.VerificationType = exports.$Enums.VerificationType = {
  EMAIL_VERIFICATION: 'EMAIL_VERIFICATION',
  PASSWORD_RESET: 'PASSWORD_RESET',
  TWO_FACTOR_AUTH: 'TWO_FACTOR_AUTH',
  PHONE_VERIFICATION: 'PHONE_VERIFICATION',
  ACCOUNT_RECOVERY: 'ACCOUNT_RECOVERY'
};

exports.VerificationStatus = exports.$Enums.VerificationStatus = {
  PENDING: 'PENDING',
  SENT: 'SENT',
  DELIVERED: 'DELIVERED',
  FAILED: 'FAILED',
  EXPIRED: 'EXPIRED',
  VERIFIED: 'VERIFIED',
  CANCELLED: 'CANCELLED'
};

exports.CustomerStatus = exports.$Enums.CustomerStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  BLOCKED: 'BLOCKED',
  VIP: 'VIP'
};

exports.BusinessType = exports.$Enums.BusinessType = {
  PRODUCT: 'PRODUCT',
  SERVICE: 'SERVICE',
  BOTH: 'BOTH'
};

exports.SessionStatus = exports.$Enums.SessionStatus = {
  STOPPED: 'STOPPED',
  STARTING: 'STARTING',
  SCAN_QR_CODE: 'SCAN_QR_CODE',
  WORKING: 'WORKING',
  FAILED: 'FAILED'
};

exports.BotObjective = exports.$Enums.BotObjective = {
  SALES: 'SALES',
  SUPPORT: 'SUPPORT',
  BOOKING: 'BOOKING',
  LEAD_GENERATION: 'LEAD_GENERATION',
  CUSTOMER_SUPPORT: 'CUSTOMER_SUPPORT',
  CLOSING: 'CLOSING',
  APPOINTMENT: 'APPOINTMENT'
};

exports.ServiceType = exports.$Enums.ServiceType = {
  ON_DEMAND: 'ON_DEMAND',
  SCHEDULED: 'SCHEDULED',
  RECURRING: 'RECURRING'
};

exports.OrderStatus = exports.$Enums.OrderStatus = {
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  PROCESSING: 'PROCESSING',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED'
};

exports.AppointmentStatus = exports.$Enums.AppointmentStatus = {
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  NO_SHOW: 'NO_SHOW'
};

exports.FollowUpType = exports.$Enums.FollowUpType = {
  PRE_SERVICE_REMINDER: 'PRE_SERVICE_REMINDER',
  POST_SERVICE_CHECK: 'POST_SERVICE_CHECK',
  POST_SERVICE_FOLLOWUP: 'POST_SERVICE_FOLLOWUP',
  DELIVERY_CONFIRMATION: 'DELIVERY_CONFIRMATION',
  PRODUCT_SATISFACTION: 'PRODUCT_SATISFACTION',
  REORDER_REMINDER: 'REORDER_REMINDER',
  ABANDONED_CART: 'ABANDONED_CART',
  LEAD_NURTURING: 'LEAD_NURTURING',
  MAINTENANCE_REMINDER: 'MAINTENANCE_REMINDER',
  CUSTOM: 'CUSTOM'
};

exports.TriggerEvent = exports.$Enums.TriggerEvent = {
  APPOINTMENT_CREATED: 'APPOINTMENT_CREATED',
  APPOINTMENT_COMPLETED: 'APPOINTMENT_COMPLETED',
  ORDER_CREATED: 'ORDER_CREATED',
  ORDER_DELIVERED: 'ORDER_DELIVERED',
  CONVERSATION_ABANDONED: 'CONVERSATION_ABANDONED',
  LEAD_QUALIFIED: 'LEAD_QUALIFIED',
  CUSTOM_SCHEDULE: 'CUSTOM_SCHEDULE'
};

exports.FollowUpStatus = exports.$Enums.FollowUpStatus = {
  PENDING: 'PENDING',
  SENT: 'SENT',
  RESPONDED: 'RESPONDED',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  FAILED: 'FAILED'
};

exports.FollowUpSentiment = exports.$Enums.FollowUpSentiment = {
  POSITIVE: 'POSITIVE',
  NEGATIVE: 'NEGATIVE',
  NEUTRAL: 'NEUTRAL',
  NEEDS_HELP: 'NEEDS_HELP'
};

exports.PaymentProvider = exports.$Enums.PaymentProvider = {
  TRIPAY: 'TRIPAY',
  MIDTRANS: 'MIDTRANS',
  XENDIT: 'XENDIT',
  DOKU: 'DOKU',
  FASPAY: 'FASPAY',
  MANUAL: 'MANUAL'
};

exports.PaymentTransactionStatus = exports.$Enums.PaymentTransactionStatus = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  PAID: 'PAID',
  FAILED: 'FAILED',
  EXPIRED: 'EXPIRED',
  CANCELLED: 'CANCELLED',
  REFUNDED: 'REFUNDED'
};

exports.SubscriptionStatus = exports.$Enums.SubscriptionStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  CANCELLED: 'CANCELLED',
  EXPIRED: 'EXPIRED',
  SUSPENDED: 'SUSPENDED',
  TRIAL: 'TRIAL'
};

exports.SubscriptionPaymentStatus = exports.$Enums.SubscriptionPaymentStatus = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  PAID: 'PAID',
  FAILED: 'FAILED',
  EXPIRED: 'EXPIRED',
  CANCELLED: 'CANCELLED',
  REFUNDED: 'REFUNDED'
};

exports.VoucherType = exports.$Enums.VoucherType = {
  PERCENTAGE: 'PERCENTAGE',
  FIXED_AMOUNT: 'FIXED_AMOUNT'
};

exports.Prisma.ModelName = {
  User: 'User',
  UserSecurity: 'UserSecurity',
  UserVerification: 'UserVerification',
  UserSession: 'UserSession',
  Business: 'Business',
  Customer: 'Customer',
  BusinessDetail: 'BusinessDetail',
  BusinessOnboarding: 'BusinessOnboarding',
  WahaSession: 'WahaSession',
  BotProfile: 'BotProfile',
  BotSetting: 'BotSetting',
  BotProfileTemplate: 'BotProfileTemplate',
  Product: 'Product',
  Service: 'Service',
  Order: 'Order',
  OrderItem: 'OrderItem',
  Appointment: 'Appointment',
  FAQ: 'FAQ',
  FAQAnalytics: 'FAQAnalytics',
  MediaAttachment: 'MediaAttachment',
  FAQMediaAttachment: 'FAQMediaAttachment',
  MessageMediaAttachment: 'MessageMediaAttachment',
  GeneratedContent: 'GeneratedContent',
  Conversation: 'Conversation',
  Message: 'Message',
  BlacklistedToken: 'BlacklistedToken',
  RefreshToken: 'RefreshToken',
  FollowUpTemplate: 'FollowUpTemplate',
  FollowUpSchedule: 'FollowUpSchedule',
  PaymentGateway: 'PaymentGateway',
  PaymentTransaction: 'PaymentTransaction',
  Role: 'Role',
  Permission: 'Permission',
  RolePermission: 'RolePermission',
  BusinessUser: 'BusinessUser',
  Package: 'Package',
  UserSubscription: 'UserSubscription',
  SubscriptionPayment: 'SubscriptionPayment',
  UserPackageUsage: 'UserPackageUsage',
  AdminPaymentGateway: 'AdminPaymentGateway',
  Voucher: 'Voucher'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
