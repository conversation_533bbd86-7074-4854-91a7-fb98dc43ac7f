{"name": "server", "version": "0.0.1", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "bun --watch run src/index.ts"}, "dependencies": {"@prisma/client": "^6.13.0", "bcryptjs": "^2.4.3", "hono": "^4.8.10", "jsonwebtoken": "^9.0.2", "prisma": "^6.13.0", "shared": "workspace:*", "zod": "^4.0.14"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/bun": "latest", "@types/jsonwebtoken": "^9.0.7"}}